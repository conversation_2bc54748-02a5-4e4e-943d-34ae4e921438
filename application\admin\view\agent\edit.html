<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-field="nickname" data-source="user/user" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id}">
        </div>
    </div>
<!--    {if condition="($admin.hierarchy <= 1)"}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Platform_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-platform_id" data-rule="required" data-field="name" data-source="platform/index" class="form-control selectpage" name="row[platform_id]" type="text" value="{$row.platform_id}">
        </div>
    </div>
    {else /}
    <input id="c-platform_id" name="row[platform_id]" type="hidden" value="{$row.platform_id}" />
    {/if}
    {if condition="($admin.hierarchy < 3)"}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_id" data-rule="required" data-params='{"custom[platform_id]":{$row.platform_id}}' data-source="agent/index" class="form-control selectpage" name="row[agent_id]" type="text" value="{$row.agent_id}">
        </div>
    </div>
    {else /}
    <input id="c-agent_id" name="row[agent_id]" type="hidden" value="{$row.agent_id}" />
    {/if}-->
    <input id="c-platform_id" name="row[platform_id]" type="hidden" value="{$row.platform_id}" />
    <input id="c-agent_id" name="row[agent_id]" type="hidden" value="{$row.agent_id}" />
<!--    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-code" data-rule="required" class="form-control" name="row[code]" type="text" value="{$row.code}" readonly>
        </div>
    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Notes')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-notes" class="form-control " rows="5" name="row[notes]" cols="50">{$row.notes}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Join_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-join_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[join_time]" type="text" value="{:$row.join_time?datetime($row.join_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Fcbl')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-fcbl" data-rule="required" class="form-control" step="0.01" name="row[fcbl]" type="number" value="{$row.fcbl}">
        </div>
    </div>
<!--    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Province')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-province" data-rule="required" class="form-control" name="row[province]" type="number" value="{$row.province}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('City')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-city" data-rule="required" class="form-control" name="row[city]" type="number" value="{$row.city}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Area')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-area" data-rule="required" class="form-control" name="row[area]" type="number" value="{$row.area}">
        </div>
    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>

    <div class="form-group">
        <label for="username" class="control-label col-xs-12 col-sm-2">管理员账号:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="username" name="admin[username]" value="{$admin.username}" data-rule="required;username" />
        </div>
    </div>
    <div class="form-group">
        <label for="username" class="control-label col-xs-12 col-sm-2">管理员密码:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="password" class="form-control" id="password" name="admin[password]" value="" />
        </div>
    </div>
    <div class="form-group">
        <label for="email" class="control-label col-xs-12 col-sm-2">Email:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="email" class="form-control" id="email" name="admin[email]" value="{$admin.email}" data-rule="required;email" />
        </div>
    </div>
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
