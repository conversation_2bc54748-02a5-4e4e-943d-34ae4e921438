<?php

namespace app\admin\controller;

use app\common\controller\Backend;

use app\admin\library\Auth;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 订单
 *
 * @icon fa fa-circle-o
 */
class Order extends Backend
{
    
    /**
     * Order模型对象
     * @var \app\admin\model\Order
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Order;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("payTypesList", $this->model->getPayTypesList());
        $this->view->assign("payStatusList", $this->model->getPayStatusList());
        $this->view->assign("isBranchList", $this->model->getIsBranchList());
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax())
        {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField'))
            {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            list($mywhere) = $this->mybuildparams();
            $total = $this->model
                    ->with(['platform','agent','user','hospital'])
                    ->where($where)
                    ->where($mywhere)
                    ->where(['use_status' => 2])
                    ->order($sort, $order)
                    ->count();

            $list = $this->model
                    ->with(['platform','agent','user','hospital'])
                    ->where($where)
                    ->where($mywhere)
                    ->where(['use_status' => 2])
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();

            foreach ($list as $row) {
                
                $row->visible([
                    'id',
                    'platform_id',
                    'agent_id',
                    'hospital_id',
                    'hospital_fcbl',
                    'hospital_price',
                    'hospital_hourlong',
                    'hospital_freedt',
                    'departments_id',
                    'equipment_id',
                    'equipment_info_id',
                    'sn',
                    'user_id',
                    'money',
                    'status',
                    'pay_types',
                    'pay_status',
                    'pay_time',
                    'createtime',
                    'returntime',
                    'updatetime',
                    'is_branch',
                    'info'
                    ]);
                $row->visible(['platform']);
                $row->getRelation('platform')->visible(['name']);
                $row->visible(['agent']);
                $row->getRelation('agent')->visible(['name']);
                $row->visible(['user']);
                $row->getRelation('user')->visible(['nickname','mobile']);
                $row->visible(['hospital']);
                $row->getRelation('hospital')->visible(['id','name']);
            }
            $list = collection($list)->toArray();
            $aturnover = $this->model->with(['platform','agent','user','hospital'])->where($where)->where($mywhere)->where(['use_status' => 2,'pay_status' => 1])->sum('order.money');
            $result = array("total" => $total, "rows" => $list,'statistics' => ['aturnover' => $aturnover]);

            return json($result);
        }
        return $this->view->fetch();
    }
    
    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                
                //验证是否能够修改
                $order_info = db('order')->where(['id'=>$ids])->find();
                
                //根据修改前的订单状态进行判断
                switch ($order_info['status']) {
                    case 1:
                        //订单使用中
                        //修改使用中的订单，需要验证必填字段是否填写
                        if($params['status'] != 1){
                            if($params['returntime'] == ''){
                                $params['returntime'] = date('Y-m-d H:i:s');
                            }
                        }
                        break;
                    case 2:
                        //订单待支付
                        //1、待支付的订单不能再设置为使用中
                        if($params['status'] == 1){
                            $this->error('待支付的订单，不能设置为使用中');exit();
                        }
                        break;
                    case 3:
                        //订单已完成
                        $this->error('订单已完成，不能进行编辑');exit();
                        break;
                    default:
                        break;
                }
                
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    if(array_key_exists('money', $params)){
                        $pay = db('pay')->where(['order_id' => $ids])->find();
                        if($params['money'] != $pay['money']){
                            $pay_data = array(
                                'sn' => $this->getOrdersn('pay'),
                                'money' => $params['money']
                            );
                            db('pay')->where(['id' => $pay['id']])->update($pay_data);
                        }
                    }
                    
                    if($order_info['status'] == 1 && $params['status'] == 2){
                        //使用中 to 待支付
                        $return = $this->orderUpdateStatus1to2($order_info['sn']);
//                        $this->error($return);exit();
                    }elseif($order_info['status'] == 1 && $params['status'] == 3){
                        //使用中 to 已完成
                        $this->orderUpdateStatus1to3($order_info['sn']);
                    }elseif($order_info['status'] == 2 && $params['status'] == 3){
                        //待支付 to 已完成
                        $this->orderUpdateStatus2to3($order_info['sn']);
                    }
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }
    
    /*
     * 更新订单状态为待支付状态
     */
    public function orderUpdateStatus1to2($sn) {
//        $sn = 'ord2019080709342547369825';
        $time = time();//当前时间戳
        $order_info = db('order')->where(['sn' => $sn])->find();
        
        $timelong = ceil(($order_info['returntime'] - $order_info['createtime']) / 3600); //计算时长（小时）
        if($timelong <= 0){
            $timelong = 1;
        }
        $timelong_fenzhong = ceil(($order_info['returntime'] - $order_info['createtime']) / 60); //计算时长（分钟）
//        die('==' . $timelong . '==' . $order_info['hospital_hourlong'] . '==' . $order_info['hospital_price']);
        $money = ($timelong / $order_info['hospital_hourlong']) * $order_info['hospital_price']; //计算费用（元）

        $order_update_data = array(
            'status' => 2,
            'timelong' => $timelong,
            'timelong_fenzhong' => $timelong_fenzhong,
            'money' => $money,
        );

        if ($timelong_fenzhong > $order_info['hospital_freedt']) {
            $order_update_data['pay_status'] = 1;
        } else {
            $order_update_data['pay_status'] = 3;
        }

        $res = db('order')->where(['id' => $order_info['id']])->update($order_update_data);
        
        $equipment_info_update_data = array(
            'status' => 1,
        );
        //更新设备信息为 = 未租用
        db('equipment_info')->where(['id' => $order_info['equipment_info_id']])->update($equipment_info_update_data);

        //重新查询更改后的订单数据
        $order_info = db('order')->where(['sn' => $sn])->find();

        
        
        //验证是否短时免单
        if ($order_info['timelong_fenzhong'] > $order_info['hospital_freedt']) {
            //不短时免单
           
            //查询是否已经生成支付订单
            $pay = db('pay')->where(['order_id'=>$order_info['id']])->find();
            if(!$pay){
                //生成支付订单
                $pay_data = array(
                    'sn' => $this->getOrdersn('pay'),
                    'user_id' => $order_info['user_id'],
                    'types' => 2,
                    'status' => 1,
                    'money' => $order_info['money'],
                    'order_id' => $order_info['id'],
                    'createtime' => time(),
                    'updatetime' => time(),
                );
                $pay_id = db('pay')->insertGetId($pay_data);
            }
        
        }
    }
    
    /*
     * 更新订单状态为待支付状态
     */
    public function orderUpdateStatus1to3($sn) {
        $time = time();
        $order_info = db('order')->where(['sn' => $sn])->find();

        $equipment_info_update_data = array(
            'status' => 1,
        );
        //更新设备信息为 = 未租用
        db('equipment_info')->where(['id' => $order_info['equipment_info_id']])->update($equipment_info_update_data);


        $order_update_data = array(
            'status' => 3,
            'pay_status' => 4,
            'is_branch' => 3
        );
        db('order')->where(['id' => $order_info['id']])->update($order_update_data);
    }
    
    /*
     * 更新订单状态为待支付状态
     */
    public function orderUpdateStatus2to3($sn) {
        $time = time();
        $order_info = db('order')->where(['sn' => $sn])->find();

        $order_update_data = array(
            'status' => 3,
            'pay_status' => 4,
            'is_branch' => 3
        );
        db('order')->where(['id' => $order_info['id']])->update($order_update_data);
        db('pay')->where(['order_id'=>$order_info['id']])->update(array('status'=>4));
        
    }
    
    
    //随机生成订单编号
    private function getOrdersn($surface = 'order') {
        $no = 'ord' . date('YmdHis') . rand(10000000, 99999999);
        if (db($surface)->where('sn', $no)->find()) {
            $no = $this->getOrdersn();
        } else {
            return $no;
        }
    }
    
    
}
