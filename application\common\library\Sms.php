<?php

namespace app\common\library;

use think\Hook;

/**
 * 短信验证码类
 */
class Sms
{

    /**
     * 验证码有效时长
     * @var int
     */
    protected static $expire = 120;

    /**
     * 最大允许检测的次数
     * @var int
     */
    protected static $maxCheckNums = 10;
    
    private $username;
    private $password;
    private $comid;
    private $company_name;
    private $errNo = array(
        "1" => "短信发送成功",
        "-2" => "除时间外，所有参数不能为空",
        "-3" => "用户名密码不正确",
        "-4" => "平台不存在",
        "-5" => "客户短信数量为0",
        "-6" => "客户账户余额小于要发送的条数",
        "-7" => "不能超过70个字",
        "-8" => "非法短信内容",
        "-9" => "未知系统故障",
        "-10" => "网络性错误",
        "-21" => "代表要加签名"
    );

    public function __construct()
    {
        $this->username = 'gongxiangchuang';
        $this->password = 'gongxiangchuang2019';
        $this->comid = '4047';
        $this->company_name = '【康护云】';
    }
    
    
    
    public function getError($no)
    {
        return $this->errNo[$no];
    }

    public function sendSms($mobile, $code,$sms_content)
    {
        if (empty($mobile) || empty($sms_content)) {
            return ['status' => 0,'msg' => '信息错误'];
        }
        $mobile = urlencode(iconv("UTF-8","gbk",$mobile));
        $sms_content = urlencode(iconv("UTF-8","gbk",$this->company_name.$sms_content));
        $time = time();
        $sendUrl = 'http://jiekou.56dxw.com/sms/HttpInterface.aspx?comid=' . $this->comid . '&username=' . $this->username . '&userpwd=' . $this->password . '&handtel=' .$mobile . '&sendcontent=' .$sms_content . '&sendtime='. $time .'&smsnumber=10690';
        $sendNo = file_get_contents($sendUrl);

        if($sendNo == 1){
            $data  = array(
                'event' => 1,
                'mobile' => $mobile,
                'code' => $code,
                'times' => 0,
                'ip' => request()->ip(),
                'createtime' => time(),
            );
            $res = db('sms')->insertGetId($data);
            if($res){
                return [
                    'status' => 1,
                    'msg' => '发送短信成功'
                ];
            }else{
                return [
                    'status' => 0,
                    'msg' => '系统错误'
                ];
            }
        }else{
            return [
                'status' => 0,
                'msg' => $this->getError($sendNo)
            ];
        }

    }
    
    
    

    /**
     * 获取最后一次手机发送的数据
     *
     * @param   int    $mobile 手机号
     * @param   string $event  事件
     * @return  Sms
     */
    public static function get($mobile, $event = 'default')
    {
        $sms = \app\common\model\Sms::
        where(['mobile' => $mobile, 'event' => $event])
            ->order('id', 'DESC')
            ->find();
        Hook::listen('sms_get', $sms, null, true);
        return $sms ? $sms : null;
    }

    /**
     * 发送验证码
     *
     * @param   int    $mobile 手机号
     * @param   int    $code   验证码,为空时将自动生成4位数字
     * @param   string $event  事件
     * @return  boolean
     */
    public static function send($mobile, $code = null, $event = 'default')
    {
        $code = is_null($code) ? mt_rand(1000, 9999) : $code;
        $time = time();
        $ip = request()->ip();
        $sms = \app\common\model\Sms::create(['event' => $event, 'mobile' => $mobile, 'code' => $code, 'ip' => $ip, 'createtime' => $time]);
        $result = Hook::listen('sms_send', $sms, null, true);
        if (!$result) {
            $sms->delete();
            return false;
        }
        return true;
    }

    /**
     * 发送通知
     *
     * @param   mixed  $mobile   手机号,多个以,分隔
     * @param   string $msg      消息内容
     * @param   string $template 消息模板
     * @return  boolean
     */
    public static function notice($mobile, $msg = '', $template = null)
    {
        $params = [
            'mobile'   => $mobile,
            'msg'      => $msg,
            'template' => $template
        ];
        $result = Hook::listen('sms_notice', $params, null, true);
        return $result ? true : false;
    }

    /**
     * 校验验证码
     *
     * @param   int    $mobile 手机号
     * @param   int    $code   验证码
     * @param   string $event  事件
     * @return  boolean
     */
    public static function check($mobile, $code, $event = 'default')
    {
        $time = time() - self::$expire;
        $sms = \app\common\model\Sms::where(['mobile' => $mobile, 'event' => $event])
            ->order('id', 'DESC')
            ->find();
        if ($sms) {
            if ($sms['createtime'] > $time && $sms['times'] <= self::$maxCheckNums) {
                $correct = $code == $sms['code'];
                if (!$correct) {
                    $sms->times = $sms->times + 1;
                    $sms->save();
                    return false;
                } else {
                    $result = Hook::listen('sms_check', $sms, null, true);
                    return $result;
                }
            } else {
                // 过期则清空该手机验证码
                self::flush($mobile, $event);
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * 清空指定手机号验证码
     *
     * @param   int    $mobile 手机号
     * @param   string $event  事件
     * @return  boolean
     */
    public static function flush($mobile, $event = 'default')
    {
        \app\common\model\Sms::
        where(['mobile' => $mobile, 'event' => $event])
            ->delete();
        Hook::listen('sms_flush');
        return true;
    }
    
    
}
