Jcrop Image Cropping Plugin
===========================

Jcrop is the quick and easy way to add image cropping functionality to
your web application. It combines the ease-of-use of a typical jQuery
plugin with a powerful cross-platform DHTML cropping engine that is
faithful to familiar desktop graphics applications.

### Feature Overview

  * Attaches unobtrusively to any image or block element
  * Completely based on true prototypical Javascript objects for extreme flexibility
  * Supports multiple active selections, per-selection customization
  * Supports aspect ratio locking, minimum/maximum size, and other features
  * Acts as in-line form element, can receive focus, tab through
  * Keyboard support for nuding selections and trapping other keys
  * Inherently API-driven and stylable with CSS
  * Mobile touch support for iOS and Android

### Cross-platform Compatibility

The current version of Jcrop has been cross-platform tested and core functionality
works in all the following browsers:

  * Firefox 3+
  * Safari 4+
  * Opera 9.5+
  * Google Chrome 14+
  * Internet Explorer 7+

Older versions of some browsers may also work.

Always thoroughly test any desired functionality on all target platforms and devices.

##### Legacy IE Compatibility

Internet Explorer 6 suffers some visual problems with the new CSS structure
and will not be explicitly supported from v2.x and up. Currently <PERSON>crop can still
be used in IE6, it just looks ugly. IE7 and newer versions deliver a nearly flawless
Jcrop experience.

-------------
## MIT License

**Jcrop is free software under MIT License.**

#### Copyright (c) 2008-2015 Tapmodo Interactive LLC,<br />http://github.com/tapmodo/Jcrop

Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

