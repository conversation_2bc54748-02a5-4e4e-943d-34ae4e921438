<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Feature Panel | Jcrop Demos</title>
    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.10.2/jquery.min.js"></script>
    <script src="../js/Jcrop.js"></script>
    <script src="//code.jquery.com/ui/1.10.3/jquery-ui.js"></script>
    <script src="http://jcrop-dev.tapmodo.com/Jcrop/js/jquery.color.js"></script>
    <script type="text/javascript">
      var cb, filter;
      
      jQuery(function($){
      
        var $targ = $('#target');
        var $easing = $('#easing');
        
        $('#target').Jcrop({
          animEasing: 'easeOutQuart',
          bgOpacity: .35,
          linked: false,
          multi: true
        },function(){
          this.container.addClass('jcrop-dark jcrop-hl-active');
          interface_load(this);
        });
        
        function interface_load(obj){
          cb = obj;
          
          cb.newSelection();
          cb.setSelect([ 147, 55, 456, 390 ]);
          cb.refresh();
          // Hack a "special" selection...
          var logosel = cb.newSelection().update($.Jcrop.wrapFromXywh([73,268,400,100]));
          
          $.extend(logosel,{
            special: true, // custom value used in our local script here
            bgColor: '#999',
            bgOpacity: 0.8,
            canResize: false,
            canDelete: false
          });
          
          logosel.element.prepend('<img src="http://tapmodo.com/img/tapmodo-official.png" style="position:absolute;background-color:white;width:100%;height:100%;" />');
          logosel.aspectRatio = 400/100;
          logosel.refresh();
          cb.ui.multi[1].focus();
          
          // Select an interesting easing function
          $easing[0].selectedIndex = 24;
          
          // Set up the easing function select element event
          cb.opt.animEasing = $easing.change(function(e){
            var $targ = $(e.target);
            cb.opt.animEasing = $targ.val();
            e.preventDefault();
            cb.ui.selection.animateTo([Math.random()*300,Math.random()*200,(Math.random()*540)+50,(Math.random()*340)+60]);
          }).val();
          
          $('#filter-selections input').attr('checked',false);
          $('#page-interface').on('startselect',function(e){
            e.preventDefault();
          });
          
          /**
           *  
           */
          cb.container.on('cropfocus cropblur cropstart cropend',function(e){
            var sel = $(e.target).data('selection');
            switch(e.type){
              case 'cropfocus':
                $('#can_size')[0].checked = sel.canResize?true:false;
                $('#can_delete')[0].checked = sel.canDelete?true:false;
                $('#can_drag')[0].checked = sel.canDrag?true:false;
                $('#set_minsize')[0].checked = (sel.minSize[0]>8)?true:false;
                $('#set_maxsize')[0].checked = (sel.maxSize[0])?true:false;
                $('#set_bounds')[0].checked = (sel.edge.n)?true:false;
                $('#is_linked')[0].disabled = sel.special?true:false;
                $('#is_linked')[0].checked = sel.linked?true:false;
                $('#shading-tools a')[0].disabled = sel.special?true:false;
                $('#shading-tools a')[sel.special?'addClass':'removeClass']('disabled');
                
                $('#ar-links').find('.active').removeClass('active');
                if (sel.aspectRatio) {
                  if (!$('#ar-links').find('[data-value="'+sel.aspectRatio+'"]').addClass('active').length)
                    $('#ar-lock').addClass('active');
                } else $('#ar-free').addClass('active');
            }
          });
          
          $('#aspect_ratio').on('change',function(e){
            var s = cb.ui.selection;
            var b = s.get();
            s.aspectRatio = e.target.checked? b.w/b.h : 0;
            s.refresh();
          });
          $('#is_linked').on('change',function(e){
            cb.ui.selection.linked = e.target.checked;
          });
          
          $('#selection-options').on('change','[data-filter-toggle]',function(e){
            var tog = $(e.target).data('filter-toggle');
            var o = { };
            o[tog] = e.target.checked? true: false;
            cb.setOptions(o);
          });
          
          var cycle_colors = [
            'red',
            'blue',
            'gray',
            'yellow',
            'orange',
            'green',
            'white'
          ];
          
          function random_coords() {
            return [
              Math.random()*300,
              Math.random()*200,
              (Math.random()*540)+50,
              (Math.random()*340)+60
            ];
          }
          
          $('#can_drag,#can_size,#can_delete,#enablesel,#multisel,#anim_mode').attr('checked','checked');
          $('#is_linked,#aspect_ratio').attr('checked',false);
          
          function anim_mode(){
            return document.getElementById('anim_mode').checked;
          }
          
          // A simple function to cleanup multiple spawned selections
          function run_cleanup(){
            var m = cb.ui.multi, s = cb.ui.selection;
            
            for(var i=0;i<m.length;i++)
              if (s !== m[i]) m[i].remove();
              
            cb.ui.multi = [ s ];
            s.center();
            s.focus();
          }
          
          // Animate button event
          $(document.body).on('click','[data-action]',function(e){
            var $targ = $(e.target);
            var action = $targ.data('action');
            
            switch(action){
              case 'set-maxsize':
                cb.setOptions({ maxSize: e.target.checked? [400,350]: [0,0] });
                break;
              case 'set-minsize':
                cb.setOptions({ minSize: e.target.checked? [60,60]: [8,8] });
                break;
              case 'set-bounds':
                if (e.target.checked){
                  cb.setOptions({ edge: {
                    n: 15,
                    e: -20,
                    s: -40,
                    w: 28
                  }});
                }
                else {
                  cb.setOptions({ edge: {
                    n: 0,
                    e: 0,
                    s: 0,
                    w: 0
                  }});
                }
                break;
              case 'set-image':
                $targ.parent().find('.active').removeClass('active');
                $targ.addClass('active');
                $('#target').attr('src','//jcrop-cdn.tapmodo.com/assets/images/'+$targ.data('image')+'-750.jpg');
                break;
              case 'set-ar':
                var value = $targ.data('value');
                $targ.parent().find('.active').removeClass('active');
                if (value == 'lock'){
                  var b = cb.ui.selection.get();
                  value = b.w / b.h;
                }
                $targ.addClass('active');
                cb.setOptions({ aspectRatio: value });
                break;
              case 'set-selmode':
                $targ.parent().find('.active').removeClass('active');
                $targ.addClass('active');
                switch($targ.data('mode')){
                  case 'none':
                    cb.container.addClass('jcrop-nodrag');
                    cb.setOptions({ allowSelect: false });
                    break;
                  case 'single':
                    cb.container.removeClass('jcrop-nodrag');
                    cb.setOptions({ allowSelect: true, multi: false });
                    break;
                  case 'multi':
                    cb.container.removeClass('jcrop-nodrag');
                    cb.setOptions({ allowSelect: true, multi: true });
                    break;
                }
                break;
              case 'enable-selections':
                cb.ui.stage.dragger.active = e.target.checked;
                break;
              case 'enable-multi':
                cb.ui.stage.dragger.multi = e.target.checked;
                break;
              case 'color-cycle':
                var cc = cycle_colors.shift();
                cb.setOptions({ bgColor: cc });
                cycle_colors.push(cc);
                break;
              case 'set-opacity':
                $targ.parent().find('.active').removeClass('active');
                $targ.addClass('active');
                cb.setOptions({ bgOpacity: $targ.data('opacity'), bgColor: 'black' });
                break;
              case 'cleanup-all':
                run_cleanup();
                break;
              case 'random-move':
                cb[anim_mode()?'animateTo':'setSelect'](random_coords());
                break;
            }
            
          }).on('keydown',function(e){
            if (e.keyCode == 8) e.preventDefault();
          }).on('selectstart',function(e){
            e.preventDefault();
          }).on('click','a[data-action]',function(e){
            e.preventDefault();
          });
        }
        
      });
      
    </script>
    <link rel="stylesheet" href="demo_files/main.css">
    <link rel="stylesheet" href="demo_files/demos.css">
    <link rel="stylesheet" href="../css/Jcrop.css">
  </head>
  <body>
    <div class="navbar navbar-fixed-top">
      <div class="navbar-inner">
        <div class="container">
          <button type="button" data-toggle="collapse" data-target="nav-collapse" class="btn btn-navbar"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></button><a href="../" class="brand">Jcrop</a>
          <div class="nav-collapse collapse">
            <ul class="nav">
              <li class="active"><a href="./basic.html">Demos</a>
              </li>
              <li><a href="http://beta.jcrop.org/doc/">Docs</a>
              </li>
              <li><a href="http://beta.jcrop.org/contact/">Contact</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="container">
      <div class="row">
        <div class="span12">
          <div class="jc-demo-box">
            <div class="page-header">
              <h1>Feature Panel</h1>
            </div>
            <div class="demo-nav menu-box">
              <h3>Jcrop Demos</h3>
              <ul class="links">
                <li><a href="basic.html">Hello World</a></li>
                <li><a href="thumbnail.html">Thumbnail Preview</a></li>
                <li><b>Feature Panel</b></li>
                <li><a href="coords.html">Dimensions</a></li>
                <li><a href="circle.html">Circles</a></li>
              </ul>
            </div>
            <div class="page-interface"><img src="http://jcrop-cdn.tapmodo.com/assets/images/sierra2-750.jpg" id="target">
              <div class="row-fluid">
                <div class="span4">
                  <h5>Selection Properties</h5>
                  <div id="selection-options">
                    <label class="checkbox">
                      <input type="checkbox" data-filter-toggle="canDrag" id="can_drag">Draggable
                    </label>
                    <label class="checkbox">
                      <input type="checkbox" data-filter-toggle="canResize" id="can_size">Resizable
                    </label>
                    <label class="checkbox">
                      <input type="checkbox" data-filter-toggle="canDelete" id="can_delete">Deletable
                    </label>
                  </div>
                  <label class="checkbox">
                    <input type="checkbox" data-action="set-bounds" id="set_bounds">Enable boundary
                  </label>
                  <label class="checkbox">
                    <input type="checkbox" data-action="set-minsize" id="set_minsize">Minimum Size
                  </label>
                  <label class="checkbox">
                    <input type="checkbox" data-action="set-maxsize" id="set_maxsize">Maximum Size
                  </label>
                </div>
                <div class="span4">
                  <h5>Aspect Ratio</h5>
                  <div class="btn-toolbar"><span id="ar-links" class="btn-group"><a href="#" data-action="set-ar" data-value="0" id="ar-free" class="btn active btn-small">Free</a><a href="#" data-action="set-ar" data-value="lock" id="ar-lock" class="btn btn-small">Lock</a><a href="#" data-action="set-ar" data-value="1.4" class="btn btn-small">Wide</a><a href="#" data-action="set-ar" data-value=".8" class="btn btn-small">Tall</a></span></div>
                  <h5>Shading</h5>
                  <div id="shading-tools" class="btn-toolbar"><a href="#" data-action="color-cycle" class="btn btn-small">Color</a><span class="btn-group"><a href="#" data-action="set-opacity" data-opacity=".35" class="btn active btn-small">Light</a><a href="#" data-action="set-opacity" data-opacity=".55" class="btn btn-small">Medium</a><a href="#" data-action="set-opacity" data-opacity=".75" class="btn btn-small">Dark</a></span></div>
                  <h5>Animation</h5>
                  <label class="checkbox">
                    <input type="checkbox" id="anim_mode">Enable Animation
                  </label>
                  <select data-action="update-easing" id="easing" class="select">
                    <option>linear</option>
                    <option>swing</option>
                    <option>easeInOutQuad</option>
                    <option>easeInQuad</option>
                    <option>easeOutQuad</option>
                    <option>easeInOutExpo</option>
                    <option>easeInExpo</option>
                    <option>easeOutExpo</option>
                    <option>easeInOutSine</option>
                    <option>easeInSine</option>
                    <option>easeOutSine</option>
                    <option>easeOutBounce</option>
                    <option>easeInOutCirc</option>
                    <option>easeInCirc</option>
                    <option>easeOutCirc</option>
                    <option>easeInOutCubic</option>
                    <option>easeInCubic</option>
                    <option>easeOutCubic</option>
                    <option>easeOutElastic</option>
                    <option>easeInOutBack</option>
                    <option>easeInBack</option>
                    <option>easeOutBack</option>
                    <option>easeInOutQuart</option>
                    <option>easeInQuart</option>
                    <option>easeOutQuart</option>
                    <option>easeInOutQuint</option>
                    <option>easeInQuint</option>
                    <option>easeOutQuint</option>
                  </select>
                </div>
                <div class="span4">
                  <h5>Current Selection</h5>
                  <label class="checkbox">
                    <input type="checkbox" data-filter-toggle="ratio" id="is_linked">Linked Selection
                  </label>
                  <div class="btn-toolbar"><a href="#" data-action="random-move" id="moveselection" class="btn btn-small">Move</a><a href="#" data-action="cleanup-all" class="btn btn-small">Cleanup</a></div>
                  <h5>New Selections</h5>
                  <div class="btn-toolbar"><span class="btn-group"><a href="#" data-action="set-selmode" data-mode="none" class="btn btn-small">None</a><a href="#" data-action="set-selmode" data-mode="single" class="btn btn-small">Single</a><a href="#" data-action="set-selmode" data-mode="multi" class="btn active btn-small">Multi</a></span></div>
                  <h5>Test Image</h5>
                  <div class="btn-toolbar"><span class="btn-group"><a href="#" data-action="set-image" data-image="sierra" class="btn btn-small">Sierra1</a><a href="#" data-action="set-image" data-image="sierra2" class="btn active btn-small">Sierra2</a><a href="#" data-action="set-image" data-image="market" class="btn btn-small">Market</a></span></div>
                </div>
              </div>
            </div>
            <div class="tapmodo-footer"><a href="http://tapmodo.com" class="tapmodo-logo segment">tapmodo.com</a>
              <div class="segment"><b>&copy; 2008-2013 Tapmodo Interactive LLC</b>
                <div>Jcrop is free software released under <a href="../MIT-LICENSE.txt">MIT License</a></div>
              </div>
            </div>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>