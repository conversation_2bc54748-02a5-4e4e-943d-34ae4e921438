<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Platform_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-platform_id" data-rule="required" data-source="platform/index" class="form-control selectpage" name="row[platform_id]" type="text" value="{$row.platform_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_id" data-rule="required" data-source="agent/index" class="form-control selectpage" name="row[agent_id]" type="text" value="{$row.agent_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-code" data-rule="required" class="form-control" name="row[code]" type="text" value="{$row.code}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Addr')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-addr" class="form-control" name="row[addr]" type="text" value="{$row.addr}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Join_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-join_type" data-rule="required" class="form-control selectpicker" name="row[join_type]">
                {foreach name="joinTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.join_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Fcbl')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-fcbl" class="form-control" step="0.01" name="row[fcbl]" type="number" value="{$row.fcbl}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-price" data-rule="required" class="form-control" step="0.01" name="row[price]" type="number" value="{$row.price}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hourlong')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hourlong" data-rule="required" class="form-control" name="row[hourlong]" type="number" value="{$row.hourlong}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Freedt')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-freedt" data-rule="required" class="form-control" name="row[freedt]" type="number" value="{$row.freedt}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Notes')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-notes" class="form-control " rows="5" name="row[notes]" cols="50">{$row.notes}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Corpname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-corpname" class="form-control" name="row[corpname]" type="text" value="{$row.corpname}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Kefu')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-kefu" class="form-control" name="row[kefu]" type="text" value="{$row.kefu}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Logo_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-logo_image" data-rule="required" class="form-control" size="50" name="row[logo_image]" type="text" value="{$row.logo_image}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-logo_image" class="btn btn-danger plupload" data-input-id="c-logo_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-logo_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-logo_image" class="btn btn-primary fachoose" data-input-id="c-logo_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-logo_image"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-logo_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Introduce_content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-introduce_content" class="form-control editor" rows="5" name="row[introduce_content]" cols="50">{$row.introduce_content}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Route')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-route" class="form-control" name="row[route]" type="text" value="{$row.route}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Balance')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-balance" class="form-control" step="0.01" name="row[balance]" type="number" value="{$row.balance}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
