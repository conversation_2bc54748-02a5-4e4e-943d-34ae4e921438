{"name": "layui-layer", "realname": "layer", "version": "3.1.1", "mobile": "2.0.0", "description": "Web弹层组件", "main": "src/layer.js", "license": "MIT", "scripts": {"run": "gulp"}, "repository": {"type": "https", "url": "git+https://github.com/sentsin/layer.git"}, "author": "贤心", "homepage": "http://layer.layui.com/", "devDependencies": {"gulp": "^3.9.0", "gulp-minify-css": "^1.2.4", "gulp-uglify": "^1.5.4", "gulp-rename": "^1.2.2", "gulp-header": "^1.8.8", "del": "^2.2.2"}, "bugs": {"url": "https://github.com/sentsin/layer/issues"}, "directories": {"test": "test"}, "dependencies": {}, "keywords": ["layer", "dialog", "tips", "alert", "confirm", "window"]}