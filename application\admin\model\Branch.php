<?php

namespace app\admin\model;

use think\Model;


class Branch extends Model
{

    

    //数据库
    protected $connection = 'database';
    // 表名
    protected $name = 'branch';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'types_text'
    ];
    

    
    public function getTypesList()
    {
        return ['1' => __('Types 1'), '2' => __('Types 2'), '3' => __('Types 3')];
    }


    public function getTypesTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['types']) ? $data['types'] : '');
        $list = $this->getTypesList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function order()
    {
        return $this->belongsTo('Order', 'order_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
