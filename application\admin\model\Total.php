<?php

namespace app\admin\model;

use think\Model;


class Total extends Model
{

    

    //数据库
    protected $connection = 'database';
    // 表名
    protected $name = 'hospital';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'join_type_text',
        'status_text'
    ];
    

    
    public function getJoinTypeList()
    {
        return ['1' => __('Join_type 1'), '2' => __('Join_type 2'), '3' => __('Join_type 3')];
    }

    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2')];
    }


    public function getJoinTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['join_type']) ? $data['join_type'] : '');
        $list = $this->getJoinTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }




}
