<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Config;

/**
 * 控制台
 *
 * @icon fa fa-dashboard
 * @remark 用于展示当前系统中的统计数据、统计报表及重要实时数据
 */
class Dashboard extends Backend
{
    protected $noNeedRight = ['tongji'];

    /**
     * 查看
     */
    public function index()
    {
        $seventtime = \fast\Date::unixtime('day', -7);
        $paylist = $createlist = [];
        for ($i = 0; $i < 7; $i++)
        {
            $day = date("Y-m-d", $seventtime + ($i * 86400));
            $createlist[$day] = mt_rand(20, 200);
            $paylist[$day] = mt_rand(1, mt_rand(1, $createlist[$day]));
        }
        $hooks = config('addons.hooks');
        $uploadmode = isset($hooks['upload_config_init']) && $hooks['upload_config_init'] ? implode(',', $hooks['upload_config_init']) : 'local';
        $addonComposerCfg = ROOT_PATH . '/vendor/karsonzhang/fastadmin-addons/composer.json';
        Config::parse($addonComposerCfg, "json", "composer");
        $config = Config::get("composer");
        $addonVersion = isset($config['version']) ? $config['version'] : __('Unknown');
        
        list($mywhere) = $this->mybuildparams();
        
        $order_count = db('order')->where($mywhere)->count();
        $pay_status3 = db('order')->where($mywhere)->where(['pay_status'=>3])->count();
        $pay_status1 = db('order')->where($mywhere)->where(['pay_status'=>1])->count();
        $order_money = db('order')->where($mywhere)->where(['status'=> 3,'pay_status'=> 1])->sum('money');
        $pay_status4 = db('order')->where($mywhere)->where(['pay_status'=>4])->count();
        $pay_status2 = db('order')->where($mywhere)->where(['pay_status'=>2])->count();
        
        $user_count = db('user')->count();//会员总数
        
        $todayMoney = 0;//今日总收益
        $yesterMoney = 0;//昨日总收益
        
        $where = [];
        if($this->adminInfo['hierarchy'] != 1){
            $where = array(
                'member_id' => $this->adminInfo['detils']['id'],
            );
            
            $whereTime =  strtotime(date("Y-m-d 00:00:00")).','.strtotime(date("Y-m-d 23:59:59"));
            $todayMoney = db('branch')->where($where)->where('createtime', 'between', $whereTime)->sum('money');
            
            $whereTime =  strtotime(date("Y-m-d 00:00:00",strtotime("-1 day"))).','.strtotime(date("Y-m-d 23:59:59",strtotime("-1 day")));
            $yesterMoney = db('branch')->where($where)->where('createtime', 'between', $whereTime)->sum('money');
        }
        
        switch ($this->adminInfo['types']) {
            case 1:
                $where = array();
                break;
            case 2:
                $where = array('platform_id' => $this->adminInfo['detils']['id']);
                break;
            case 3:
                $where = array('agent_id' => $this->adminInfo['detils']['id']);
                break;
            case 4:
                $where = array('hospital_id' => $this->adminInfo['detils']['id']);
                break;
            default:
                break;
        }
        
        $lastmonth = 0;//上月总收益
        $thismonth = 0;//本月累计收益
        
        $whereTime = strtotime(date("Y-m-01 00:00:00", strtotime("-1 month"))) . ',' . strtotime(date("Y-m-30 23:59:59", strtotime("-1 month")));
        $lastmonth = db('order')->where($where)->where('createtime', 'between', $whereTime)->sum('money');
        
        
        $whereTime = strtotime(date("Y-m-01 00:00:00")) . ',' . time();
        $thismonth = db('order')->where($where)->where('createtime', 'between', $whereTime)->sum('money');
        
        //正在使用
        $where = array(
            'status' => 1,
        );
        if($this->adminInfo['types'] == 2){
            $where['platform_id'] = $this->adminInfo['detils']['id'];
        }elseif($this->adminInfo['types'] == 3){
            $where['agent_id'] = $this->adminInfo['detils']['id'];
        }elseif($this->adminInfo['types'] == 4){
            $where['hospital_id'] = $this->adminInfo['detils']['id'];
        }
        
        $order_status1_count = db('order')->where($where)->count();
        
        
        
        $this->view->assign([
            
            'order_count' => $order_count,
            'pay_status3' => $pay_status3,
            'pay_status1' => $pay_status1,
            'order_money' => $order_money,
            'pay_status4' => $pay_status4,
            'pay_status2' => $pay_status2,
            
            'user_count' => $user_count,
            'todayMoney' => $todayMoney,
            'yesterMoney' => $yesterMoney,
            
            'lastmonth' => $lastmonth,
            'thismonth' => $thismonth,
            
            'order_status1_count' =>$order_status1_count,
            
            
            'totaluser'        => 35200,
            'totalviews'       => 219390,
            'totalorder'       => 32143,
            'totalorderamount' => 174800,
            'todayuserlogin'   => 321,
            'todayusersignup'  => 430,
            'todayorder'       => 2324,
            'unsettleorder'    => 132,
            'sevendnu'         => '80%',
            'sevendau'         => '32%',
            'paylist'          => $paylist,
            'createlist'       => $createlist,
            'addonversion'       => $addonVersion,
            'uploadmode'       => $uploadmode
        ]);

        return $this->view->fetch();
    }
    
    
    
    public function tongji(){
        $Statistics_Type = 1;//统计类型 1 天统计 2 月统计 3 年统计
        $Statistics_Duration = 7; //统计时长 单位（年or月or日）
        
        $where = array();
        
        /*yss 自定义查询条件*/
        if($this->adminInfo['hierarchy'] >= 2){
            $where['platform_id'] = $this->adminInfo['platform_id'];
        }
        
        if($this->adminInfo['hierarchy'] >= 3){
            if($this->adminInfo['types'] == 3){
                $where['agent_id'] = $this->adminInfo['detils']['id'];
            }elseif($this->adminInfo['types'] == 4){
                $where['agent_id'] = $this->adminInfo['detils']['id'];
            }
        }
        
        
        
        //统计时间段
        
        $currentDate = date("Y-m-d",time());
        $timeSlot = array();
        switch ($Statistics_Type) {
            case 1:
                $weekarray=array("日","一","二","三","四","五","六");
                
                for($i = $Statistics_Duration;$i>= 1;$i--){
                    $time = date("Y-m-d",strtotime("-".$i." day"));
                    $timeSlot[] = array(
                        'time' => $time,
                        'start' => $time . ' 00:00:00',
                        'end' => $time . ' 23:59:59',
                        'zhou' => '周'.$weekarray[date('w',  strtotime($time))],
                    );
                }
                break;
             case 2:
                 date("Y-m-d",strtotime("last month"));
                break;
             case 3:
                 date("Y-m-d",strtotime("-1 year"));
                break;

            default:
                break;
        }
        
        
        $option = array(
            'tooltip' => array('trigger' => 'axis','axisPointer' => array('type' => 'shadow',),),
            'legend' => array('data' => array('租用次数','短时免单','正常支付','交易额'),),
            'grid' => array('left' => '3%','right' => '4%','bottom' => '3%','containLabel' => true),
            'xAxis' => array('type' => 'category','data' => array(),),
            'yAxis' => array('type' => 'value',),
            'series' => array(
                array('name' => '租用次数','type' => 'bar','data' => array(),),
                array('name' => '短时免单','type' => 'bar','stack' => '订单','data' => array(),),
                array('name' => '正常支付','type' => 'bar', 'stack' => '订单','data' => array(),),
                array('name' => '交易额','type' => 'bar','data' => array(),),
            ),
        );
        
        foreach($timeSlot as $k => $v){
            $option['xAxis']['data'][] = $timeSlot[$k]['zhou'];
            
            $option['series'][0]['data'][] = db('order')->where($where)->where(['createtime'=>array('between',[strtotime($timeSlot[$k]['start']),strtotime($timeSlot[$k]['end'])])])->count();
            
            $option['series'][1]['data'][] = db('order')
                    ->where($where)
                    ->where(['createtime'=>array('between',[strtotime($timeSlot[$k]['start']),strtotime($timeSlot[$k]['end'])])])
                    ->where(['status'=>3,'pay_status'=>3])
                    ->count();
//            
            $option['series'][2]['data'][] = db('order')
                    ->where($where)
                    ->where(['createtime'=>array('between',[strtotime($timeSlot[$k]['start']),strtotime($timeSlot[$k]['end'])])])
                    ->where(['status'=>3,'pay_status'=>1])
                    ->count();
//            
            $option['series'][3]['data'][] = db('order')
                    ->where($where)
                    ->where(['createtime'=>array('between',[strtotime($timeSlot[$k]['start']),strtotime($timeSlot[$k]['end'])])])
                    ->where(['status'=>3,'pay_status'=>1])
                    ->sum('money');
            
            
        }
        
        
        
        
        return json($option);
    }

}
