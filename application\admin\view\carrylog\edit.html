<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

<!--    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Types')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-types" class="form-control selectpicker" name="row[types]">
                {foreach name="typesList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.types"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Member_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-member_id" data-rule="required" data-source="member/index" class="form-control selectpage" name="row[member_id]" type="text" value="{$row.member_id}">
        </div>
    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-money" class="form-control" step="0.01" name="row[money]" type="number" value="{$row.money}" readonly="readonly">
        </div>
    </div>
<!--    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tx_states')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select data-select-only="true"  id="c-tx_states" class="form-control selectpicker" name="row[tx_states]">
                {foreach name="txStatesList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.tx_states"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bank_khh')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bank_khh" class="form-control" name="row[bank_khh]" type="text" value="{$row.bank_khh}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bank_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bank_name" class="form-control" name="row[bank_name]" type="text" value="{$row.bank_name}" readonly="readonly">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bank_card')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bank_card" class="form-control" name="row[bank_card]" type="text" value="{$row.bank_card}" readonly="readonly">
        </div>
    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Info')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-info" class="form-control " rows="5" name="row[info]" cols="50" readonly="readonly">{$row.info}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('States')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-states" class="form-control selectpicker" name="row[states]">
                {foreach name="statesList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.states"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Admin_info')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-admin_info" class="form-control " rows="5" name="row[admin_info]" cols="50">{$row.admin_info}</textarea>
        </div>
    </div>
    {if condition="$row.states == 1 "}
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
    {/if}
    
</form>
