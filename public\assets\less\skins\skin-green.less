/*
 * Skin: Green
 * -----------
 */
@import "../bootstrap-less/mixins.less";
@import "../bootstrap-less/variables.less";
@import "../fastadmin/variables.less";
@import "../fastadmin/mixins.less";

.skin-green {
  //Navbar
  .main-header {
    .navbar {
      .navbar-variant(@green; #fff);
      .sidebar-toggle {
        color: #fff;
        &:hover {
          background-color: darken(@green, 5%);
        }
      }
      @media (max-width: @screen-header-collapse) {
        .dropdown-menu {
          li {
            &.divider {
              background-color: rgba(255, 255, 255, 0.1);
            }
            a {
              color: #fff;
              &:hover {
                background: darken(@green, 5%);
              }
            }
          }
        }
      }
    }
    //Logo
    .logo {
      .logo-variant(darken(@green, 5%));
    }

    li.user-header {
      background-color: @green;
    }
  }

  //Content Header
  .content-header {
    background: transparent;
  }

  //Create the sidebar skin
  .skin-dark-sidebar(@green);

}
