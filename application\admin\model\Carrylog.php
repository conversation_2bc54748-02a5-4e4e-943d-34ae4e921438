<?php

namespace app\admin\model;

use think\Model;


class Carrylog extends Model
{

    

    //数据库
    protected $connection = 'database';
    // 表名
    protected $name = 'carrylog';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'types_text',
        'tx_states_text',
        'states_text'
    ];
    

    
    public function getTypesList()
    {
        return ['1' => __('Types 1'), '2' => __('Types 2'), '3' => __('Types 3')];
    }

    public function getTxStatesList()
    {
        return ['1' => __('Tx_states 1')];
    }
//    , '2' => __('Tx_states 2')

    public function getStatesList()
    {
        return ['1' => __('States 1'), '2' => __('States 2'), '3' => __('States 3')];
    }


    public function getTypesTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['types']) ? $data['types'] : '');
        $list = $this->getTypesList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getTxStatesTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['tx_states']) ? $data['tx_states'] : '');
        $list = $this->getTxStatesList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatesTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['states']) ? $data['states'] : '');
        $list = $this->getStatesList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function admin()
    {
        return $this->belongsTo('Admin', 'admin_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
