<div class="navbar navbar-default navbar-fixed-top" role="navigation">
  <div class="container">

    <!-- Collapsed navigation -->
    <div class="navbar-header">
      {% if include_nav or include_next_prev or repo_url %}
      <!-- Expander button -->
      <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
        <span class="sr-only">Toggle navigation</span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
      </button>
      {% endif %}

      <!-- Main title -->
      <a class="navbar-brand" href="{{ homepage_url }}">{{ site_name }}</a>
    </div>

    <!-- Expanded navigation -->
    <div class="navbar-collapse collapse">
      {% if include_nav %}
      <!-- Main navigation -->
      <ul class="nav navbar-nav">
        {% for nav_item in nav %}
        {% if nav_item.children %}
        <li class="dropdown{% if nav_item.active %} active{% endif %}">
          <a href="#" class="dropdown-toggle" data-toggle="dropdown">{{ nav_item.title }} <b class="caret"></b></a>
          <ul class="dropdown-menu">
            {% for nav_item in nav_item.children %}
            {% include "nav-sub.html" %}
            {% endfor %}
          </ul>
        </li>
        {% else %}
        <li {% if nav_item.active %}class="active"{% endif %}>
          <a href="{{ nav_item.url }}">{{ nav_item.title }}</a>
        </li>
        {% endif %}
        {% endfor %}
      </ul>
      {% endif %}

      <ul class="nav navbar-nav navbar-right">
        {% if repo_url %}
        <li>
          <a href="{{ repo_url }}">
            {% if repo_name == 'GitHub' %}
            <i class="fa fa-github"></i>
            {% elif repo_name == 'Bitbucket' %}
            <i class="fa fa-bitbucket"></i>
            {% endif %}
            {{ repo_name }}
          </a>
        </li>
        {% endif %}
      </ul>
    </div>
  </div>
</div>
