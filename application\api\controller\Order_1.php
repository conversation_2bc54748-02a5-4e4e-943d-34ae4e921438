<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use fast\Random;
use think\Validate;
use weixin\WXBizDataCrypt;
use weixin\WeixinPay;

/**
 * 订单接口
 */
class Order extends Api {

    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    public function _initialize() {
        parent::_initialize();
    }

    /*
     * 创建订单
     * id 床id
     */

    public function index() {
        $id = input('id', null);
        $id = 137;
        if ($id) {
            //查询床信息
            $equipment_info = db('equipment_info')->where(['id' => $id])->find();
            if ($equipment_info) {
                //查询这个设备是否有状态为正在使用中的订单
                $order_conduct = db('order')->where(['equipment_info_id' => $equipment_info['id'],'status' => 1])->find();
                if($order_conduct){
                    //有未结束的订单
                    print_r($equipment_info);
                    print_r($order_conduct);
                }
                
                die();
                //查询主设备信息
                $equipment = db('equipment')->where(['id' => $equipment_info['equipment_id']])->find();
                if ($equipment['status'] == 1) {
                    //医院信息
                    $hospital = db('hospital')->where(['id' => $equipment_info['hospital_id']])->find();

                    $info = $this->equipment_info_lujing($equipment_info);

                    $is_maintain = db('user')->where(['id' => $this->uid])->value('is_maintain');

                    if ($is_maintain == 1) {

                        $order = db('order')->where(['user_id' => $this->uid, 'status' => ['<>', 3]])->find();
                        if (!$order) {
                            $order = array(
                                'platform_id' => $equipment_info['platform_id'],
                                'agent_id' => $equipment_info['agent_id'],
                                'hospital_id' => $equipment_info['hospital_id'],
                                'hospital_fcbl' => $hospital['fcbl'],
                                'hospital_price' => $hospital['price'],
                                'hospital_hourlong' => $hospital['hourlong'],
                                'hospital_freedt' => $hospital['freedt'],
                                'departments_id' => $equipment_info['departments_id'],
                                'equipment_id' => $equipment_info['equipment_id'],
                                'equipment_info_id' => $equipment_info['id'],
                                'info' => $info,
                                'sn' => $this->getOrdersn(),
                                'user_id' => $this->uid,
                                'status' => 1,
                                'pay_types' => 0,
                                'pay_status' => 0,
                                'createtime' => time(),
                                'updatetime' => time(),
                            );

                            $res = db('order')->insertGetId($order);
                            if ($res) {
                                db('equipment_info')->where(['id' => $id])->update(array('status' => 2));
                                $this->success('生成订单成功');
                            } else {
                                $this->error('生成订单失败');
                            }
                        } else {
                            $this->error('您的订单以生成');
                        }
                    } else {
                        $maintain_data = array(
                            'platform_id' => $equipment_info['platform_id'],
                            'agent_id' => $equipment_info['agent_id'],
                            'hospital_id' => $equipment_info['hospital_id'],
                            'departments_id' => $equipment_info['departments_id'],
                            'equipment_id' => $equipment_info['equipment_id'],
                            'equipment_info_id' => $equipment_info['id'],
                            'info' => $info,
                            'user_id' => $this->uid,
                            'createtime' => time(),
                        );
                        $res = db('maintain')->insertGetId($maintain_data);
                        if ($res) {
                            db('equipment_info')->where(['id' => $id])->update(array('status' => 2));
                            $this->success('生成维护记录成功');
                        } else {
                            $this->error('生成维护记录失败');
                        }
                    }
                } else {
                    $this->error('主设备被禁用');
                }
            } else {
                $this->error('设备不存在');
            }
        } else {
            $this->error('参数错误');
        }
    }

    /*
     * 还床订单确认
     */

    public function orderInfo() {
        $kpl = input('id', null);
        if ($kpl) {
            $equipment_info = db('equipment_info')->where(['kpl' => $kpl])->find();
            if ($equipment_info) {
                switch ($equipment_info['status']) {
                    case 1:
                        //设备未租用状态
                        $this->error('设备未租用');
                        break;
                    case 2:
                        //设备租用中，根据  设备的id  会员id 订单的状态 进行查询有无符合条件的订单
                        $order = db('order')->where(['equipment_info_id' => $equipment_info['id'], 'user_id' => $this->uid, 'status' => 1])->field('id,platform_id,agent_id,hospital_id,departments_id,equipment_id,equipment_info_id,sn as ordercode,user_id,status,pay_types,pay_status,createtime as sdt,updatetime')->find();
                        if ($order) {
                            $order['sdt'] = date('Y-m-d H:i:s', $order['sdt']);
                            //查询医院信息
                            $hospital = db('hospital')->where(['id' => $order['hospital_id']])->find();
                            $order['price'] = $hospital['price'];
                            $order['hourlong'] = $hospital['hourlong'];
                            $return = array(
                                'order' => $order,
                                'device' => $equipment_info,
                            );
                            $this->success('数据查询成功', $return);
                        } else {
                            $this->error('无该设备租用信息');
                        }
                        break;
                    case 3:
                        $this->error('设备故障');
                        break;
                    default:
                        $this->error('系统错误');
                        break;
                }
            } else {
                $this->error('设备不存在');
            }
        } else {
            $this->error('参数错误');
        }
    }

    public function aa() {
        $exemption_count = db('platform')->where(['id' => 358])->value('exemption_count');

        $order_exemption_count = db('order')->where([
                    //'returntime'=>['>=',  strtotime(date('Y-m-d 00:00:00')),'<=',  strtotime(date('Y-m-d 23:59:59'))],
                    'returntime' => array('between', array(
                            strtotime(date('Y-m-d 00:00:00')),
                            strtotime(date('Y-m-d 23:59:59'))
                    )),
                    'user_id' => 358,
                    'status' => 3
                ])
//                ->where(['returntime'=>['<=',  strtotime(date('Y-m-d 23:59:59'))]])
//                ->where(['returntime'=>['>=',  strtotime(date('Y-m-d 00:00:00'))]])
                ->count();
        die('==' . $order_exemption_count);
    }

    /*
     * 结束订单
     * ordercode 订单编号
     */

    public function orderUpdateStatus() {
        $sn = input('ordercode', null);
        $time = time();
        $order_info = db('order')->where(['sn' => $sn])->find();
        
        if ($order_info['status'] == 1) {
            //支付状态还未更改

            $timelong = ceil(($time - $order_info['createtime']) / 3600); //计算时长（小时）
            $timelong_fenzhong = ceil(($time - $order_info['createtime']) / 60); //计算时长（分钟）
            $money = ($timelong / $order_info['hospital_hourlong']) * $order_info['hospital_price']; //计算费用（元）
            
            $order_update_data = array(
                'status' => 2,
                'returntime' => $time,
                'timelong' => $timelong,
                'timelong_fenzhong' => $timelong_fenzhong,
                'money' => $money,
            );

            if ($timelong_fenzhong > $order_info['hospital_freedt']) {
                //如果使用时长大于免费时间
                $order_update_data['pay_status'] = 1;
            } else {
//                $order_update_data['pay_status'] = 3;
                $exemption_count = db('platform')->where(['id' => $order_info['platform_id']])->value('exemption_count');
                $order_exemption_count = db('order')->where([
//                    'returntime'=>['>=',  strtotime(date('Y-m-d 00:00:00'))],
//                    'returntime'=>['<=',  strtotime(date('Y-m-d 23:59:59'))],
                            'returntime' => array('between', array(
                                    strtotime(date('Y-m-d 00:00:00')),
                                    strtotime(date('Y-m-d 23:59:59'))
                            )),
                            'user_id' => $order_info['user_id'],
                            'status' => 3
                        ])->count();
                if ($order_exemption_count < $exemption_count) {
                    $order_update_data['pay_status'] = 3;
                } else {
                    $order_update_data['pay_status'] = 1;
                }
            }
            $res = db('order')->where(['id' => $order_info['id']])->update($order_update_data);
            if (!$res) {
                $this->error('更新状态失败');
            } else {
                $equipment_info_update_data = array(
                    'status' => 1,
                );
                //更新设备信息为 = 未租用
                db('equipment_info')->where(['id' => $order_info['equipment_info_id']])->update($equipment_info_update_data);
            }
            //重新查询更改后的订单数据
            $order_info = db('order')->where(['sn' => $sn])->find();
        }


        //验证是否短时免单  $order_info['timelong_fenzhong'] > $order_info['hospital_freedt']
        if ($order_info['pay_status'] == 1) {
            //不短时免单
            //查询是否已经生成支付订单
            $pay = db('pay')->where(['order_id' => $order_info['id']])->find();
            if (!$pay) {
                //生成支付订单
                $pay_data = array(
                    'sn' => $this->getOrdersn('pay'),
                    'user_id' => $order_info['user_id'],
                    'types' => 2,
                    'status' => 1,
                    'money' => $order_info['money'],
                    'order_id' => $order_info['id'],
                    'createtime' => time(),
                    'updatetime' => time(),
                );
                $pay_id = db('pay')->insertGetId($pay_data);
                if (!$pay_id) {
                    $this->error('生成支付订单失败');
                    exit();
                }
            }
        }
        $this->success('查询成功', $order_info);
    }
    
    /**
     * 处理结束订单的逻辑
     * @param array $order_info 订单信息
     */
    public function orderEnd($order_info){
        
    }


    /*
     * 订单支付 - 微信支付
     */

    public function orderWeixin() {
        $sn = input('ordercode', null);
        $order_info = db('order')->where(['sn' => $sn])->find();

        if ($order_info) {
            if ($order_info['status'] == 2) {
                $pay = db('pay')->where(['order_id' => $order_info['id']])->find();
                if ($pay) {
                    $return = $this->payment($pay['id']);
                    if ($return['success']) {
                        $this->success('加载成功', $return['data']);
                    } else {
                        $this->error($return['msg']);
                    }
                } else {
                    $this->error('支付信息不存在');
                }
            } else {
                $this->error('订单状态错误');
            }
        } else {
            $this->error('订单不存在');
        }
    }

    /*
     * 订单支付 - 短时免单
     */

    public function orderFreeSheet() {
        $sn = input('ordercode', null);
        $order_info = db('order')->where(['sn' => $sn])->find();

        $time = time();
        $order_update_data = array(
            'status' => 3,
            'pay_types' => 0,
            'pay_status' => 3,
            'updatetime' => time(),
        );
        $res = db('order')->where(['id' => $order_info['id']])->update($order_update_data);
        if ($res) {
            $this->success('操作成功');
        } else {
            $this->error('更新状态失败');
        }
    }

    /*
     * 保证金充值
     */

    public function bondRecharge() {
        $user = db('user')->where(['id' => $this->uid])->find();
        if ($user['deposit'] > 0) {
            $this->error('已充值保证金');
        } else {
            $pay = db('pay')->where(['user_id' => $this->uid, 'status' => ['in', '2,5'], 'types' => 1])->find();
            if ($pay) {
                $this->error('已充值保证金');
            } else {
                $bond = db('config')->where(['id' => 18])->value('value');
                //生成支付订单
                $pay_data = array(
                    'sn' => $this->getOrdersn('pay'),
                    'user_id' => $this->uid,
                    'types' => 1,
                    'status' => 1,
                    'money' => $bond,
                    'createtime' => time(),
                    'updatetime' => time(),
                );
                $res = db('pay')->insertGetId($pay_data);
                if ($res) {
                    $return = $this->payment($res);
                    if ($return['success']) {
                        $this->success('加载成功', $return['data']);
                    } else {
                        $this->error($return['msg']);
                    }
                } else {
                    $this->error('支付记录创建失败');
                }
            }
        }
    }

    /*
     * 发起支付
     */

    public function payment($id) {
        $return = array(
            'success' => FALSE,
        );
        $pay = db('pay')->where(['id' => $id])->find();

        if ($pay) {
            if ($pay['status'] == 1) {
                $payconfig = config('wxali.wx');
                $user = db('user')->where(['id' => $pay['user_id']])->find();
                $appid = $payconfig['xcx']['appid'];
                $openid = $user['openid'];
                $mch_id = $payconfig['sh']['mch_id'];
                $key = $payconfig['sh']['key'];
                $money = $pay['money'];
                $order_sn = $pay['sn'];
                $money = 0.01;
                $weixinPay = new WeixinPay($appid, $openid, $mch_id, $key, $money, $order_sn);
                $mentPay = $weixinPay->pay();
                $return['success'] = true;
                $return['msg'] = '发起成功';
                $return['data'] = $mentPay;
            } else {
                $return['msg'] = '订单已支付';
            }
        } else {
            $return['msg'] = '订单不存在';
        }
        return $return;
    }

    //首页扫码进入判断设备状态
    public function shebeiStatus() {

        $kpl = input('id', null);
        if ($kpl) {
            $equipment_info = db('equipment_info')->where(['kpl' => $kpl])->find();
            if ($equipment_info) {
                $this->success('数据查询成功', $equipment_info);
            } else {
                $this->error('设备不存在');
            }
        } else {
            $this->error('参数错误');
        }
    }

    /*
     * 获得我的订单
     * status 状态
     */

    public function getMyOrder() {
        $status = input('status', 2);
        $order = db('order')->where(['user_id' => $this->uid, 'status' => $status])->order('id', 'desc')->select();
        foreach ($order as $k => $v) {
            $order[$k]['createtime'] = date('Y-m-d H:i:s', $order[$k]['createtime']);
            $order[$k]['equipment_info_deviceno'] = db('equipment_info')->where(['id' => $order[$k]['equipment_info_id']])->value('deviceno');
            if ($order[$k]['status'] == 2) {
                if ($order[$k]['pay_status'] == 1) {
                    $order[$k]['statusname'] = '立即支付';
                } else {
                    $order[$k]['statusname'] = '点击免单';
                }
            } else {
                $order[$k]['statusname'] = '已完成';
            }
        }
        $count = count($order);
        $return = array(
            'data' => $order,
            'count' => $count
        );
        $this->success('数据加载成功', $return);
    }

    //随机生成订单编号
    private function getOrdersn($surface = 'order') {
        $no = 'ord' . date('YmdHis') . rand(10000000, 99999999);
        if (db($surface)->where('sn', $no)->find()) {
            $no = $this->getOrdersn();
        } else {
            return $no;
        }
    }

}
