<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

<!--    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Platform_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-platform_id" data-rule="required" data-source="platform/index" class="form-control selectpage" name="row[platform_id]" type="text" value="{$row.platform_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_id" data-rule="required" data-source="agent/index" class="form-control selectpage" name="row[agent_id]" type="text" value="{$row.agent_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hospital_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hospital_id" data-rule="required" data-source="hospital/index" class="form-control selectpage" name="row[hospital_id]" type="text" value="{$row.hospital_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Departments_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-departments_id" data-rule="required" data-source="departments/index" class="form-control selectpage" name="row[departments_id]" type="text" value="{$row.departments_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Equipment_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-equipment_id" data-rule="required" data-source="equipment/index" class="form-control selectpage" name="row[equipment_id]" type="text" value="{$row.equipment_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-code" data-rule="required" class="form-control" name="row[code]" type="text" value="{$row.code}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Devicename')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-devicename" data-rule="required" class="form-control" name="row[devicename]" type="text" value="{$row.devicename}">
        </div>
    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Deviceno')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-deviceno" class="form-control" name="row[deviceno]" type="text" value="{$row.deviceno}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Lyname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-lyname" class="form-control" name="row[lyname]" type="text" value="{$row.lyname}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mac')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mac" class="form-control" name="row[mac]" type="text" value="{$row.mac}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Servicecode')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-servicecode" class="form-control" name="row[servicecode]" type="text" value="{$row.servicecode}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Charactercode')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-charactercode" class="form-control" name="row[charactercode]" type="text" value="{$row.charactercode}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nb_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nb_number" class="form-control" name="row[nb_number]" type="text" value="{$row.nb_number}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Notes')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-notes" class="form-control " rows="5" name="row[notes]" cols="50">{$row.notes}</textarea>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
