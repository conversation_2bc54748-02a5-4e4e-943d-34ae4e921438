<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use fast\Random;
use think\Validate;
use weixin\WXBizDataCrypt;
use weixin\Wechatapppay;
/**
 * 会员接口
 */
class User extends Api
{
    protected $noNeedLogin = ['login', 'mobilelogin', 'register', 'resetpwd', 'changeemail', 'changemobile', 'third', 'weixinlogo'];
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 会员中心
     */
    public function index()
    {
        $this->success('', ['welcome' => $this->auth->nickname]);
    }

    /**
     * 会员登录
     *
     * @param string $account  账号
     * @param string $password 密码
     */
    public function login()
    {
        $account = $this->request->request('account');
        $password = $this->request->request('password');
        if (!$account || !$password) {
            $this->error(__('Invalid parameters'));
        }
        $ret = $this->auth->login($account, $password);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 手机验证码登录
     *
     * @param string $mobile  手机号
     * @param string $captcha 验证码
     */
    public function mobilelogin()
    {
        $mobile = $this->request->request('mobile');
        $captcha = $this->request->request('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (!Sms::check($mobile, $captcha, 'mobilelogin')) {
            $this->error(__('Captcha is incorrect'));
        }
        $user = \app\common\model\User::getByMobile($mobile);
        if ($user) {
            //如果已经有账号则直接登录
            $ret = $this->auth->direct($user->id);
        } else {
            $ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, []);
        }
        if ($ret) {
            Sms::flush($mobile, 'mobilelogin');
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 注册会员
     *
     * @param string $username 用户名
     * @param string $password 密码
     * @param string $email    邮箱
     * @param string $mobile   手机号
     */
    public function register()
    {
        $username = $this->request->request('username');
        $password = $this->request->request('password');
        $email = $this->request->request('email');
        $mobile = $this->request->request('mobile');
        if (!$username || !$password) {
            $this->error(__('Invalid parameters'));
        }
        if ($email && !Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        $ret = $this->auth->register($username, $password, $email, $mobile, []);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Sign up successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 注销登录
     */
    public function logout()
    {
        $this->auth->logout();
        $this->success(__('Logout successful'));
    }

    /**
     * 修改会员个人信息
     *
     * @param string $avatar   头像地址
     * @param string $username 用户名
     * @param string $nickname 昵称
     * @param string $bio      个人简介
     */
    public function profile()
    {
        $user = $this->auth->getUser();
        $username = $this->request->request('username');
        $nickname = $this->request->request('nickname');
        $bio = $this->request->request('bio');
        $avatar = $this->request->request('avatar', '', 'trim,strip_tags,htmlspecialchars');
        if ($username) {
            $exists = \app\common\model\User::where('username', $username)->where('id', '<>', $this->auth->id)->find();
            if ($exists) {
                $this->error(__('Username already exists'));
            }
            $user->username = $username;
        }
        $user->nickname = $nickname;
        $user->bio = $bio;
        $user->avatar = $avatar;
        $user->save();
        $this->success();
    }

    /**
     * 修改邮箱
     *
     * @param string $email   邮箱
     * @param string $captcha 验证码
     */
    public function changeemail()
    {
        $user = $this->auth->getUser();
        $email = $this->request->post('email');
        $captcha = $this->request->request('captcha');
        if (!$email || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if (\app\common\model\User::where('email', $email)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Email already exists'));
        }
        $result = Ems::check($email, $captcha, 'changeemail');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification = $user->verification;
        $verification->email = 1;
        $user->verification = $verification;
        $user->email = $email;
        $user->save();

        Ems::flush($email, 'changeemail');
        $this->success();
    }

    /**
     * 修改手机号 启用
     *
     * @param string $email   手机号
     * @param string $captcha 验证码
     */
    public function changemobile()
    {
        $user = $this->auth->getUser();
        $mobile = $this->request->request('mobile');
        $captcha = $this->request->request('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (\app\common\model\User::where('mobile', $mobile)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Mobile already exists'));
        }
        $result = Sms::check($mobile, $captcha, 'changemobile');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification = $user->verification;
        $verification->mobile = 1;
        $user->verification = $verification;
        $user->mobile = $mobile;
        $user->save();

        Sms::flush($mobile, 'changemobile');
        $this->success();
    }
    
        
    /**
     * 第三方登录
     *
     * @param string $platform 平台名称
     * @param string $code     Code码
     */
    public function third()
    {
        $url = url('user/index');
        $platform = $this->request->request("platform");
        $code = $this->request->request("code");
        $config = get_addon_config('third');
        if (!$config || !isset($config[$platform])) {
            $this->error(__('Invalid parameters'));
        }
        $app = new \addons\third\library\Application($config);
        //通过code换access_token和绑定会员
        $result = $app->{$platform}->getUserInfo(['code' => $code]);
        if ($result) {
            $loginret = \addons\third\library\Service::connect($platform, $result);
            if ($loginret) {
                $data = [
                    'userinfo'  => $this->auth->getUserinfo(),
                    'thirdinfo' => $result
                ];
                $this->success(__('Logged in successful'), $data);
            }
        }
        $this->error(__('Operation failed'), $url);
    }

    /**
     * 重置密码
     *
     * @param string $mobile      手机号
     * @param string $newpassword 新密码
     * @param string $captcha     验证码
     */
    public function resetpwd()
    {
        $type = $this->request->request("type");
        $mobile = $this->request->request("mobile");
        $email = $this->request->request("email");
        $newpassword = $this->request->request("newpassword");
        $captcha = $this->request->request("captcha");
        if (!$newpassword || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if ($type == 'mobile') {
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
            $user = \app\common\model\User::getByMobile($mobile);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Sms::check($mobile, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Sms::flush($mobile, 'resetpwd');
        } else {
            if (!Validate::is($email, "email")) {
                $this->error(__('Email is incorrect'));
            }
            $user = \app\common\model\User::getByEmail($email);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Ems::check($email, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Ems::flush($email, 'resetpwd');
        }
        //模拟一次登录
        $this->auth->direct($user->id);
        $ret = $this->auth->changepwd($newpassword, '', true);
        if ($ret) {
            $this->success(__('Reset password successful'));
        } else {
            $this->error($this->auth->getError());
        }
    }
    
    public function weixinlogo(){
        $post = $_REQUEST;
	$code = $post['code'];
	$userinfo = json_decode($post['userInfo'], true);
	$iv = $userinfo['iv'];
	$encryptedData = $userinfo['encryptedData'];
	$content = $this->getSession($code);
	$pc = new \weixin\WXBizDataCrypt();
        $pc->getAppID($content['appid']);
        $pc->getSessionKey($content['session_key']);
	$errCode = $pc->decryptData($encryptedData, $iv, $userdata);

	if ($errCode == 0) {
	    $user_data = json_decode($userdata, true);
            $userInfo = $this->getUser(1, $user_data['openId']);
            if(!$userInfo){
                $userInfo = $this->userAdd(1,$user_data);
                if(!$userinfo){
                    $this->error('用户信息入库失败');
                }
            }
             $this->success('返回成功', $userInfo);
	} else {
            $this->error('获取用户信息出错！1');
	}
    }
    
    /* 
     * code 换取 session_key 
     */

    public function getSession($code) {
        $s_data = array(
            'appid' => $this->wx_config['appid'],
            'secret' => $this->wx_config['AppSecret'],
            'js_code' => $code,
            'grant_type' => 'authorization_code',
        );
	$session_url = 'https://api.weixin.qq.com/sns/jscode2session?';
	$header = array();
	$content = curl_https($session_url, $s_data, $header);
	$content = json_decode($content, true);
	$content['appid'] = $s_data['appid'];
	return $content;
    }
    
    /*
     * 验证用户是否已经存在
     */
    public function getUser($type,$key){
        $where = array();
        switch ($type) {
            case 1:
                $where['openid'] = $key;
                break;
            default:
                break;
        }
        $userInfo = db('user')->where($where)->find();
        if($userInfo){
            return $userInfo;
        }else{
            return false;
        }
    }
    
    
    /*
     * 用户信息入库
     */
    public function userAdd($types,$user){
        $username = $this->rand_str();
        $password = '123456';
        $email = '';
        $mobile = '';
        $user_add_data = array(
            'avatar' => $user['avatarUrl'],
            'gender' => $user['gender'],
            'nickname' => $user['nickName'],
            'openid' => $user['openId'],
            'types' => $types,
            'joinip' => GetIP(),
            'jointime' => time(),
        );
        $ret = $this->auth->register($username, $password, $email, $mobile, $user_add_data);
        if ($ret) {
            $data = $this->auth->getUserinfo();
            return $data;
        } else {
            return false;
        }
    }
    
    /*
     * 随机生成用户名
     */
    public function rand_str($randLength = 10, $addtime = false, $includenumber = 1)
     {
         if ($includenumber) {
             $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHJKLMNPQEST123456789';
         } else {
             $chars = 'abcdefghijklmnopqrstuvwxyz';
         }
         $len = strlen($chars);
         $randStr = '';
         for ($i = 0; $i < $randLength; $i++) {
             $randStr .= $chars[mt_rand(0, $len - 1)];
         }
         $tokenvalue = $randStr;
         if ($addtime) {
             $tokenvalue = $randStr . time();
         }
         
         $res = db('user')->where(['username'=>$tokenvalue])->find();
         if($res){
             $tokenvalue = $this->rand_str();
         }
         return $tokenvalue;
     }
    
    /*
     * 判断会员是否可以借床
     */
    public function getDefault(){
        $user = db('user')->where(['id'=>  $this->uid])->find();
        if($user['is_maintain'] == 1){
            $deposit = $user['deposit'];
            $deposit_id = $user['deposit_id'];
            if($deposit_id > 0){
                //会员表中有充值记录id
                $pay = db('pay')->where(['id'=>$deposit_id])->find();
                if($pay['status'] == 5){
                    //退款中
                    $this->error('未充值保证金','',201);
                }else{
                    //充值记录正常
                    //验证会员是否有未完成的订单
                    $order = db('order')->where(['user_id'=>  $this->uid,'status' => ['<',3]])->find();
                    if($order){
                        $this->error('您有未完成的订单',$order,202);
                    }else{
                        $this->success('可以租床');
                    }
                }
            }else{
                //无充值记录
                $this->error('未充值保证金','',201);
            }
            
//            if($deposit <= 0){
////            if($pay['status'] != 2){
//                //判断会员押金余额
//                $this->error('未充值保证金','',201);
//            }else{
//                //验证会员是否有未完成的订单
//                $order = db('order')->where(['user_id'=>  $this->uid,'status' => ['<',3]])->find();
//                if($order){
//                    $this->error('您有未完成的订单',$order,202);
//                }else{
//                    $this->success('可以租床');
//                }
//            }
        }else{
            $this->success('维护员');
        }
    }
    
    
    /*
     * 修改手机号
     */
    public function updateMobile(){
        $mobile = input('mobile',NULL);
        $code = input('code',NULL);
        if($mobile != null && $code != null){
            $sms = db('sms')->where(['mobile'=>$mobile])->order('id desc')->find();
            if($sms){
                if($sms['code'] == $code){
                    $time = time();
                    if( ($time - 600 ) <= $sms['createtime']){
                        $user_data = array(
                            'mobile' => $mobile,
                        );
                        $res = db('user')->where(['id' => $this->uid])->update($user_data);
                        if($res){
                            $this->success('更新成功',$mobile);
                        }else{
                            $this->error('更新失败');
                        }
                    }else{
                        $this->error('验证码过期');
                    }
                }else{
                    $this->error('验证码错误');
                }
            }else{
                $this->error('无验证码');
            }
        }else{
            $this->error('参数错误');
        }
    }
    
    /*
     * 故障上报
     */
    public function faultAdd(){
        $kpl = input('kpl',null);
        $content = input('content',null);
        if($kpl){
            $equipment_info = db('equipment_info')->where(['kpl'=>$kpl])->find();
            if($equipment_info){

                $info = $this->equipment_info_lujing($equipment_info);    
   
                $fault_data = array(
                    'user_id' => $this->uid,
                    'platform_id' => $equipment_info['platform_id'],
                    'agent_id' => $equipment_info['agent_id'],
                    'hospital_id' => $equipment_info['hospital_id'],
                    'departments_id' => $equipment_info['departments_id'],
                    'equipment_id' => $equipment_info['equipment_id'],
                    'equipment_info_id' => $equipment_info['id'],
                    'lujing' => $info,
                    'content' => $content,
                    'status' => 1,
                    'createtime' => time(),
                    'updatetime' => time(),
                );
                $res = db('fault')->insertGetId($fault_data);
                if($res){
                    $this->success('上报成功');
                }else{
                    $this->error('上报失败');
                }
            }else{
                $this->error('设备不存在');
            }
        }else{
            $this->error('参数错误');
        }
    }
    
    //查询用户是否已经缴纳保证金
    public function userDeposit(){
        $return = array(
            'is_deposit' => 1,//1已充值 2 未充值
        );
        $user = db('user')->where(['id'=>  $this->uid])->find();
        if($user['deposit'] > 0){
            $return['deposit'] = $user['deposit'];
        }else{
            $return['is_deposit'] = 2;
        }
        $this->success('加载成功',$return);
    }
    
    //退款
    public function depositTixian(){
        $user = db('user')->where(['id'=>  $this->uid])->find();
        if($user['deposit'] > 0){
            $order = db('order')->where(['user_id'=>  $this->uid,'status'=>['<>',3]])->find();
            if(!$order){
                $pay = db('pay')->where(['id'=>$user['deposit_id']])->find();
                if($pay){
                    if($pay['status'] ==2){
                        $return = $this->wxRefund($pay);
                        if($return['success'] == true){
                            $this->success($return['msg']);
                        }else{
                            $this->error($return['msg']);
                        }
                    }else{
                        if($pay['status'] == 1){
                            $this->error('未支付');
                        }else if($pay['status'] == 3){
                            $this->error('已退款');
                        }
                    }
                }else{
                    $this->error('支付记录不存在');
                }
            }else{
                $this->error('有未完成订单');
            }
        }else{
            $this->error('未充值保证金');
        }
    }
    
    //发起退款
    public function wxRefund($pay){
        $return = array(
            'success' => FALSE,
        );
        $wxappid = $this->wx_config['appid'];
        $mch_id = $this->wx_config['mch_id'];
        $notify_url = $this->wx_config['refundNotify_url'];
        $wxkey = $this->wx_config['wxkey'];
        $apiclient_cert = $this->wx_config['apiclient_cert'];
        $apiclient_key = $this->wx_config['apiclient_key'];

        $wechatAppPay = new wechatapppay($wxappid, $mch_id, $notify_url, $wxkey, $apiclient_cert, $apiclient_key);
        //查询订单状态
        $sn = $pay['sn'];
        $transaction_id = $pay['transaction_id'];
        
        $wx_orderQuery = $wechatAppPay->orderQuery($sn);
        if($wx_orderQuery['return_code'] == 'SUCCESS'){
            if($wx_orderQuery['trade_state'] == 'SUCCESS'){
                $money = $wx_orderQuery['total_fee'];
                
                //退款
                $params = array();
                $params['out_trade_no'] = $sn;            //必填项 自定义的订单号
                $params['total_fee'] = $money;       //必填项 订单金额 单位为分所以要*100
                $params['return_oid'] = $transaction_id;   
                $params['notify_url'] = $this->wx_config['refundNotify_url'];//退款单号
                $wx_result = $wechatAppPay->refund($params);
                
                if($wx_result['return_code'] == 'SUCCESS'){
                    db('pay')->where(['id'=>$pay['id']])->update(array(
                        'status' => 5,
                        ));
                    $return['msg'] = '退款成功，等待处理';
                    $return['success'] = true;
                }else{
                    $return['msg'] = '通信失败，错误原因：'.$wx_result['return_msg'];
                }
            }else{
                switch ($wx_orderQuery['trade_state']) {
                    case 'REFUND':
                        $return['msg'] = '已退款';
                        break;
                    case 'NOTPAY':
                        $return['msg'] = '未支付';
                        break;
                    case 'CLOSED':
                        $return['msg'] = '已关闭';
                        break;
                    case 'REVOKED':
                        $return['msg'] = '已撤销';
                        break;
                    case 'USERPAYING':
                        $return['msg'] = '用户支付中';
                        break;
                    case 'PAYERROR':
                        $return['msg'] = '支付失败';
                        break;
                    default:
                        $return['msg'] = '状态错误';
                        break;
                }
            }
        }else{
            $return['msg'] = '通信失败，错误原因：'.$wx_orderQuery['return_msg'];
        }
        return $return;
    }
    
}
