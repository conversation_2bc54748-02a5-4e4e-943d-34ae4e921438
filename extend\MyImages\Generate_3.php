<?php

namespace MyImages;

use think\Db;
use think\Model;
use Think\Controller;

class Generate extends Controller {
    
    protected $canvasWidth = 770;
    protected $canvasheight = 520;
    protected $data = array();

    protected function _initialize() {
        parent::_initialize();
    }
    
    //设置画布基础信息宽度 高度
    public function setUp($canvasWidth,$canvasheight){
        $this->canvasWidth = $canvasWidth;
        $this->canvasheight = $canvasheight;
    }
    
    //添加图片元素
    public function addDataImg($types, $url, $dst_x, $dst_y, $src_x, $src_y, $w, $h) {
        $this->data[] = array(
            'types' => $types, //元素类型 1 图片 2 文字 3 圆形 4 方框 5 画一个矩形并填充 6 二维码
            'info' => array(
                'url' => $url, //图片路径
                'dst_x' => $dst_x,
                'dst_y' => $dst_y,
                'src_x' => $src_x,
                'src_y' => $src_y,
                'w' => $w,
                'h' => $h,
            )
        );
    }
    //添加文字元素
    public function addDataText($types, $size, $angle, $x, $y, $rgb, $fontfile, $text) {
        $this->data[] = array(
            'types' => $types,
            'info' => array(
                'size' => $size,
                'angle' => $angle,
                'x' => $x,
                'y' => $y,
                'rgb' => $rgb,
                'fontfile' => $fontfile,
                'text' => $text,
            )
        );
    }
    //添加形状元素
    public function addDataShape($types, $x, $y, $w, $h, $rgb) {
        $this->data[] = array(
            'types' => $types, //元素类型 1 图片 2 文字 3 圆形 4 方框 5 画一个矩形并填充 6 二维码
            'info' => array(
                'x' => $x,
                'y' => $y,
                'w' => $w,
                'h' => $h,
                'rgb' => $rgb
            )
        );
    }

    /**
     * 分享图片生成
     * @param $confData  商品数据，array
     * @param $fileName string 保存文件名,默认空则直接输入图片
     */
    function createSharePng($fileName = '') {
        //创建画布
        $im = imagecreatetruecolor($this->canvasWidth, $this->canvasheight);
        //填充画布背景色
        $color = imagecolorallocate($im, 255, 255, 255);
        imagefill($im, 0, 0, $color);
        $list = $this->data;
        foreach ($list as $k => $v) {
            $row = $list[$k];
            switch ($row['types']) {
                case 1:
                    list($g_w, $g_h) = getimagesize($row['info']['url']);
                    $Img = $this->createImageFromFile($row['info']['url']);
                    if($Img){
                        imagecopyresized($im, $Img, $row['info']['dst_x'], $row['info']['dst_y'], $row['info']['src_x'], $row['info']['src_y'], $row['info']['w'], $row['info']['h'], $g_w, $g_h);
                        imagedestroy($Img);
                    }
                    
                    break;
                case 2:
                    $color = ImageColorAllocate($im, $row['info']['rgb']['r'], $row['info']['rgb']['g'], $row['info']['rgb']['b']);
                    imagettftext($im, $row['info']['size'], $row['info']['angle'], $row['info']['x'], $row['info']['y'], $color, $row['info']['fontfile'], $row['info']['text']);
                    break;
                case 3:
                    $color = ImageColorAllocate($im, $row['info']['rgb']['r'], $row['info']['rgb']['g'], $row['info']['rgb']['b']);
                    imageellipse($im, $row['info']['x'], $row['info']['y'], $row['info']['w'], $row['info']['h'], $color);
                    break;
                case 4:
                    $color = ImageColorAllocate($im, $row['info']['rgb']['r'], $row['info']['rgb']['g'], $row['info']['rgb']['b']);
                    imagerectangle($im, $row['info']['x'], $row['info']['y'], $row['info']['w'], $row['info']['h'], $color);
                    break;
                case 5:
                    $color = ImageColorAllocate($im, $row['info']['rgb']['r'], $row['info']['rgb']['g'], $row['info']['rgb']['b']);
                    imagefilledrectangle($im, $row['info']['x'], $row['info']['y'], $row['info']['w'], $row['info']['h'], $color);
                    break;
                case 6:
                    
                    $url = 'http://'.$_SERVER['HTTP_HOST'].'/'.$this->getXcxCode($row['info']['url']);
                    list($g_w, $g_h) = getimagesize($url);
                    $Img = $this->createImageFromFile($url);
                    
                    imagecopyresized($im, $Img, $row['info']['dst_x'], $row['info']['dst_y'], $row['info']['src_x'], $row['info']['src_y'], $row['info']['w'], $row['info']['h'], $g_w, $g_h);
                    imagedestroy($Img);
                    break;
                default:
                    # code...
                    break;
            }
        }
        $imgUrl = 'public/uploads/erweima/'.$this->str_rand().'.jpg';
        $res = imagepng($im, $imgUrl);
        $return['success'] = false;
        if($res){
            $return['success'] = true;
            $return['imgurl'] = 'http://'.$_SERVER['HTTP_HOST'].'/'. $imgUrl;
        }else{
            $return['msg'] = '海报生成失败';
        }
        //释放空间
        imagedestroy($im);
        return $return;
        
//        if ($imgUrl) {
//            die($res);
//        } else {
//            Header("Content-Type: image/png");
//            imagepng($im);
//        }
    }

    /**
     * 从图片文件创建Image资源
     * @param $file 图片文件，支持url
     * @return bool|resource    成功返回图片image资源，失败返回false
     */
    function createImageFromFile($file) {
        if (preg_match('/http(s)?:\/\//', $file)) {
            $fileSuffix = $this->getNetworkImgType($file);
        } else {
            $fileSuffix = pathinfo($file, PATHINFO_EXTENSION);
        }

        if (!$fileSuffix)
            return false;

        switch ($fileSuffix) {
            case 'jpeg':
                $theImage = @imagecreatefromjpeg($file);
                break;
            case 'jpg':
                $theImage = @imagecreatefromjpeg($file);
                break;
            case 'png':
                $theImage = @imagecreatefrompng($file);
                break;
            case 'gif':
                $theImage = @imagecreatefromgif($file);
                break;
            default:
                $theImage = @imagecreatefromstring(file_get_contents($file));
                break;
        }

        return $theImage;
    }

    /**
     * 获取网络图片类型
     * @param $url  网络图片url,支持不带后缀名url
     * @return bool
     */
    function getNetworkImgType($url) {
        $ch = curl_init(); //初始化curl
        curl_setopt($ch, CURLOPT_URL, $url); //设置需要获取的URL
        curl_setopt($ch, CURLOPT_NOBODY, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3); //设置超时
        curl_setopt($ch, CURLOPT_TIMEOUT, 3);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); //支持https
        curl_exec($ch); //执行curl会话
        $http_code = curl_getinfo($ch); //获取curl连接资源句柄信息
        curl_close($ch); //关闭资源连接

        if ($http_code['http_code'] == 200) {
            $theImgType = explode('/', $http_code['content_type']);

            if ($theImgType[0] == 'image') {
                return $theImgType[1];
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * 分行连续截取字符串
     * @param $str  需要截取的字符串,UTF-8
     * @param int $row  截取的行数
     * @param int $number   每行截取的字数，中文长度
     * @param bool $suffix  最后行是否添加‘...’后缀
     * @return array    返回数组共$row个元素，下标1到$row
     */
    function cn_row_substr($str, $row = 1, $number = 10, $suffix = true) {
        $result = array();
        for ($r = 1; $r <= $row; $r++) {
            $result[$r] = '';
        }
        $str = trim($str);
        if (!$str)
            return $result;
        $theStrlen = strlen($str);
        //每行实际字节长度
        $oneRowNum = $number * 3;
        for ($r = 1; $r <= $row; $r++) {
            if ($r == $row and $theStrlen > $r * $oneRowNum and $suffix) {
                $result[$r] = $this->mg_cn_substr($str, $oneRowNum - 6, ($r - 1) * $oneRowNum) . '...';
            } else {
                $result[$r] = $this->mg_cn_substr($str, $oneRowNum, ($r - 1) * $oneRowNum);
            }
            if ($theStrlen < $r * $oneRowNum)
                break;
        }
        return $result;
    }

    /**
     * 按字节截取utf-8字符串
     * 识别汉字全角符号，全角中文3个字节，半角英文1个字节
     * @param $str  需要切取的字符串
     * @param $len  截取长度[字节]
     * @param int $start    截取开始位置，默认0
     * @return string
     */
    function mg_cn_substr($str, $len, $start = 0) {
        $q_str = '';
        $q_strlen = ($start + $len) > strlen($str) ? strlen($str) : ($start + $len);
        //如果start不为起始位置，若起始位置为乱码就按照UTF-8编码获取新start
        if ($start and json_encode(substr($str, $start, 1)) === false) {
            for ($a = 0; $a < 3; $a++) {
                $new_start = $start + $a;
                $m_str = substr($str, $new_start, 3);
                if (json_encode($m_str) !== false) {
                    $start = $new_start;
                    break;
                }
            }
        }
        //切取内容
        for ($i = $start; $i < $q_strlen; $i++) {
            //ord()函数取得substr()的第一个字符的ASCII码，如果大于0xa0的话则是中文字符
            if (ord(substr($str, $i, 1)) > 0xa0) {
                $q_str .= substr($str, $i, 3);
                $i+=2;
            } else {
                $q_str .= substr($str, $i, 1);
            }
        }
        return $q_str;
    }

    
    
    
    
    
    /**
     * [getXcxCode 获取微信小程序二维码]
     * @return [type] [小程序二维码图片]
     */
    public function getXcxCode($urlText) {

        $url = "https://api.weixin.qq.com/wxa/getwxacode?access_token=" . $this->_getAccessToken();
        $data = [
            "path" => $urlText,
            "width" => 430,
        ];

        $data = json_encode($data);
        $result = $this->_requestPost($url, $data);
        if (!$result) {
            return false;
        }
        $fileName = 'erweim';
        if ($fileName) {
            file_put_contents("public/uploads/erweima/" . $fileName . ".jpeg", $result);
            $datafile = ['fileName' => $fileName];
            return "public/uploads/erweima/" . $fileName . ".jpeg";
        }
    }

    /**
     * 获取 access_tonken
     * @param string $token_file 用来存储token的临时文件
     */
    private function _getAccessToken($token_file = './access_token') {

        // 考虑过期问题，将获取的access_token存储到某个文件中
        $life_time = 7200;
        //if (file_exists($token_file) && time() - filemtime($token_file) < $life_time) {
            // 存在有效的access_token
            //return file_get_contents($token_file);
        //}
        // 目标URL：        
        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=wx422bccb5783a34d1&secret=364aa29ea1c0fce766a43f68d247fa76";
        //向该URL，发送GET请求
        $result = $this->_requestGet($url);
        if (!$result) {
            return false;
        }
        // 存在返回响应结果
        $result_obj = json_decode($result);
        // 写入
        file_put_contents($token_file, $result_obj->access_token);
        return $result_obj->access_token;
    }

    /**
     * 发送GET请求的方法
     * @param string $url URL
     * @param bool $ssl 是否为https协议
     * @return string 响应主体Content
     */
    protected function _requestGet($url, $ssl = true) {
        // curl完成
        $curl = curl_init();

        //设置curl选项
        curl_setopt($curl, CURLOPT_URL, $url); //URL
        $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '
    Mozilla/5.0 (Windows NT 6.1; WOW64; rv:38.0) Gecko/20100101 Firefox/38.0 FirePHP/0.7.4';
        curl_setopt($curl, CURLOPT_USERAGENT, $user_agent); //user_agent，请求代理信息
        curl_setopt($curl, CURLOPT_AUTOREFERER, true); //referer头，请求来源
        curl_setopt($curl, CURLOPT_TIMEOUT, 30); //设置超时时间
        //SSL相关
        if ($ssl) {
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); //禁用后cURL将终止从服务端进行验证
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 2); //检查服务器SSL证书中是否存在一个公用名(common name)。
        }
        curl_setopt($curl, CURLOPT_HEADER, false); //是否处理响应头
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); //curl_exec()是否返回响应结果
        // 发出请求
        $response = curl_exec($curl);
        if (false === $response) {
            echo '<br>', curl_error($curl), '<br>';
            return false;
        }
        curl_close($curl);
        return $response;
    }

    /**
     * 发送GET请求的方法
     * @param string $url URL
     * @param bool $ssl 是否为https协议
     * @return string 响应主体Content
     */
    protected function _requestPost($url, $data, $ssl = true) {
        //curl完成
        $curl = curl_init();
        //设置curl选项
        curl_setopt($curl, CURLOPT_URL, $url); //URL
        $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '
        Mozilla/5.0 (Windows NT 6.1; WOW64; rv:38.0) Gecko/20100101 Firefox/38.0 FirePHP/0.7.4';
        curl_setopt($curl, CURLOPT_USERAGENT, $user_agent); //user_agent，请求代理信息
        curl_setopt($curl, CURLOPT_AUTOREFERER, true); //referer头，请求来源
        curl_setopt($curl, CURLOPT_TIMEOUT, 30); //设置超时时间
        //SSL相关
        if ($ssl) {
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); //禁用后cURL将终止从服务端进行验证
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 2); //检查服务器SSL证书中是否存在一个公用名(common name)。
        }
        // 处理post相关选项
        curl_setopt($curl, CURLOPT_POST, true); // 是否为POST请求
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data); // 处理请求数据
        // 处理响应结果
        curl_setopt($curl, CURLOPT_HEADER, false); //是否处理响应头
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); //curl_exec()是否返回响应结果
        // 发出请求
        $response = curl_exec($curl);
        if (false === $response) {
            echo '<br>', curl_error($curl), '<br>';
            return false;
        }
        curl_close($curl);
        return $response;
    }
    
 function str_rand($length = 32, $char = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ') {
        if (!is_int($length) || $length < 0) {
            return false;
        }

        $string = '';
        for ($i = $length; $i > 0; $i--) {
            $string .= $char[mt_rand(0, strlen($char) - 1)];
        }

        return $string;
    }
    
}

