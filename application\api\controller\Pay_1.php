<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use fast\Random;
use think\Validate;
use weixin\WXBizDataCrypt;
use weixin\WeixinPay;

/**
 * 订单接口
 */
class Pay extends Api
{
    protected $noNeedLogin = ['notify','refundnotify'];
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();
    }
    
    //退款回调
    public function refundnotify() {
        $xml = file_get_contents("php://input");
        $array_data = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
        $req_infoB = base64_decode($array_data['req_info'], true);//base64解密码
        $req_infoC = openssl_decrypt($req_infoB, 'aes-256-ecb', md5($this->wx_config['wxkey']), OPENSSL_RAW_DATA);//使用加密的md5进行aes-256-ecb解密，获得xml格式数据
        $req_infoD = $this->xmlToArray($req_infoC);//xml转数组
        db('errow')->insertGetId(array('info'=>json_encode($req_infoD)));
        if ($array_data['return_code'] == 'SUCCESS') {
            if($req_infoD['refund_status'] == 'SUCCESS'){
                $pay = db('pay')->where(['sn' => $req_infoD['out_trade_no']])->find();

                if ($pay) {
                    if ($pay['status'] == 5) {
                        $pay_data = array(
                            'status' => 3,
                            'refund_time' => time(),
                            'refund_id' => $req_infoD['refund_id'],
                        );
                        $res = db('pay')->where(['id' => $pay['id']])->update($pay_data);
                        if ($res) {
                            $user_data = array(
                                'deposit' => 0,
                                'deposit_id' => 0,
                            );
                            db('user')->where(['id' => $pay['user_id']])->update($user_data);
                        }
                    }
                }
            }else{
                db('pay')->where(['sn' => $req_infoD['out_trade_no']])->update(array('status'=>2));
            }
        }
    }
    
    //xml转换成数组  
    private function xmlToArray($xml) {
	//禁止引用外部xml实体   
	libxml_disable_entity_loader(true);
	$xmlstring = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
	$val = json_decode(json_encode($xmlstring), true);
	return $val;
    }
    
    //支付回调
    public function notify(){       
        
        $xml = file_get_contents("php://input");
//        $xml = '<xml><appid><![CDATA[wxe2946cbf308a8915]]></appid>
//        <bank_type><![CDATA[OTHERS]]></bank_type>
//        <cash_fee><![CDATA[1]]></cash_fee>
//        <fee_type><![CDATA[CNY]]></fee_type>
//        <is_subscribe><![CDATA[N]]></is_subscribe>
//        <mch_id><![CDATA[**********]]></mch_id>
//        <nonce_str><![CDATA[9xvc3g3sje81kqn7u9mwj3jbh1v12yys]]></nonce_str>
//        <openid><![CDATA[or8Hd4gz25wLuymQc1NLzsOYhRoM]]></openid>
//        <out_trade_no><![CDATA[ord2020061613584755332736]]></out_trade_no>
//        <result_code><![CDATA[SUCCESS]]></result_code>
//        <return_code><![CDATA[SUCCESS]]></return_code>
//        <sign><![CDATA[2B466041DA32AA7B628836C086ED3934]]></sign>
//        <time_end><![CDATA[**************]]></time_end>
//        <total_fee>1</total_fee>
//        <trade_type><![CDATA[JSAPI]]></trade_type>
//        <transaction_id><![CDATA[4200000602202006167070665128]]></transaction_id>
//        </xml>';
        $array_data = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
//        db('errow')->insertGetId(array('info'=>$xml));die();
        $pay = db('pay')->where(['sn'=>$array_data['out_trade_no']])->find();
        if($pay){
            if($pay['status'] == 1){
//                if($pay['money'] == $array_data['cash_fee']){
                    $pay_data = array(
                        'pay_type' => 1,
                        'pay_time' => time(),
                        'transaction_id' => $array_data['transaction_id'],
                        'status' => 2
                    );
                    $res = db('pay')->where(['id'=>$pay['id']])->update($pay_data);
                    
                    if($res){
                        if($pay['types'] == 1){
                            db('user')->where(['id'=>$pay['user_id']])->update(array('deposit'=>$pay['money'],'deposit_id'=>$pay['id']));
                        }
                        
                        if($pay['types'] == 2){
                            $order = db('order')->where(['id'=>$pay['order_id']])->find();
                            $order_data = array(
                                'status' => 3,
                                'pay_types' => 1,
                                'pay_status' => 1,
                                'pay_time' => time(),
                            );
                            $res = db('order')->where(['id'=>$order['id']])->update($order_data);
                            if($res){
                                $this->branchCommission($order['id']);
                            }
                        }
                    }
//                }
            }
        }
    }
    
    public function branchCommission(){
        $order_id = 3734;
        $order_info = db('order')->where(['id'=>$order_id])->find();
        if($order_info){
            if($order_info['is_branch'] == 1){
                if($order_info['status'] == 3 && $order_info['pay_status'] == 1){
                    
                    //分佣金额
                    $money = $order_info['money'];
                    
                    //计算扣除手续费的实际结算金额
                    $Service_Charge = 0.006;//手续费比例
                    $user = db('user')->where(['id'=>$order_info['user_id']])->find();//会员信息
                    $pay = db('pay')->where(['id'=>$user['deposit_id']])->find();//押金信息
                    $deposit_Service_Charge = ( $pay['money'] * $Service_Charge ) - $pay['service_charge'];//还需要扣除的押金手续费
                    
                    if($deposit_Service_Charge > 0){
                        //还有需要扣除的押金手续费
                        if($money <= $deposit_Service_Charge){
                            //分佣金额不足以一扣除手续费或者刚好够扣除押金手续费
                            //将分佣金额作为押金手续费扣除
                            db('pay')->where(['id'=>$pay['id']])->setInc('service_charge',$money);
                            db('order')->where(['id'=>$order_info['id']])->update(array('is_branch'=>2));
                            return;
                        }else{
                            //分佣金额足以扣除手续费
                            //计算剩余金额
                            //扣除押金手续费部分
                            db('pay')->where(['id'=>$pay['id']])->setInc('service_charge',$deposit_Service_Charge);
                            $surplus = $money - $deposit_Service_Charge;
                            if($surplus <= 0){
                                db('order')->where(['id'=>$order_info['id']])->update(array('is_branch'=>2));
                                return;
                            }
                            $money = $surplus;//分佣剩余金额
                        }
                    }else{
                        //押金手续费已扣完
                    }
                    //扣除租金手续费
                    $money = $money - ( $order_info['money'] * $Service_Charge );
                    if($money > 0){
                        //还有分佣金额
                        $order_info['money'] = $money;
                        /*
                        * 分润步骤
                        * 1、医院分润
                        * 2、代理商分润
                        * 3、平台分润
                        */
                       //查询设备所属医院
                       $hospital = db('hospital')->where(['id'=> $order_info['hospital_id']])->find();

                       if($hospital['fcbl'] > 0){
                           $this->hospitalBranch($order_info, $hospital);
                       }

                       $lowrFrbl = $this->agentBranch($hospital['fcbl'],$hospital['route'], $order_info);

                       $this->platformBranch($lowrFrbl, $hospital['platform_id'], $order_info);

                       db('order')->where(['id'=>$order_info['id']])->update(array('is_branch'=>2));
                    }else{
                        //未剩余分佣金额
                        db('order')->where(['id'=>$order_info['id']])->update(array('is_branch'=>2));
                    }

                }else{
                    die('不符合分佣条件');
                }
            }else{
                die('已分佣');
            }
        }else{
            die('订单不存在');
        }
    }
    
    //医院分润
    public function hospitalBranch($order,$hospital){
        $money = ( $order['money'] / 100 ) * $hospital['fcbl'];
        $res = $this->branchAdd(3, $hospital['id'], $order['id'], $order['money'], $hospital['fcbl'], $money);
        if($res){
            db('hospital')->where(['id'=>$hospital['id']])->setInc('balance', $money);
        }
    }
    
    /*
     * 代理商分润
     * lowrFrbl 下级分润比例
     * route 分润层级
     * order 订单信息
     */
    public function agentBranch($lowrFrbl,$route,$order){
        $routeArr = json_decode($route,true);
        
        foreach($routeArr as $k => $v){
            if($routeArr[$k]['fcbl'] > $lowrFrbl){
                $fcbl = $routeArr[$k]['fcbl'] - $lowrFrbl;
                $lowrFrbl = $routeArr[$k]['fcbl'];
                $money = ( $order['money'] / 100 ) * $fcbl;
                $res = $this->branchAdd(2, $routeArr[$k]['id'], $order['id'], $order['money'], $fcbl, $money);
                if($res){
                    db('agent')->where(['id'=>$routeArr[$k]['id']])->setInc('balance', $money);
                }
            }
        }
        return $lowrFrbl;
    }
    
    /*
     * 子平台分润
     */
    public function platformBranch($lowrFrbl,$platform_id,$order){
        $platform = db('platform')->where(['id'=>$platform_id])->find();
        $fcbl = $platform['fcbl'] - $lowrFrbl;
        $money = ( $order['money'] / 100 ) * $fcbl;
        $res = $this->branchAdd(1, $platform['id'], $order['id'], $order['money'], $fcbl, $money);
        if($res){
            db('platform')->where(['id'=>$platform['id']])->setInc('balance', $money);
        }
    }
    
    //生成记录
    public function branchAdd($types,$member_id,$order_id,$order_money,$fcbl,$money){
        $order_info = db('order')->where(['id'=>$order_id])->find();
        
        $branch_data = array(
            'platform_id' => $order_info['platform_id'],
            'agent_id' => $order_info['agent_id'],
            'hospital_id' => $order_info['hospital_id'],
            'departments_id' => $order_info['departments_id'],
            'equipment_id' => $order_info['equipment_id'],
            'equipment_info_id' => $order_info['equipment_info_id'],
            'types' => $types,
            'member_id' => $member_id,
            'order_id' => $order_id,
            'order_money' => $order_money,
            'fcbl' => $fcbl,
            'money' => $money,
            'createtime' => time(),
        );
        
        $res = db('branch')->insertGetId($branch_data);
        return $res;
    }

    public function a(){
        $list = db('order')
                ->where(['hospital_id' => 27,'pay_status' => 1,'status' => 3])
//                ->where(['hospital_fcbl'=>0])
//                ->where(['hospital_fcbl'=>['>',0]])
                ->select();
        foreach($list as $k => $v){
            $list[$k]['branch'] = db('branch')
                    ->where(['order_id'=> $list[$k]['id']])
//                    ->where(['types' => 2])
                    ->select();
//            if($list[$k]['branch'][0]['fcbl'] != 90){
//                print_r($list[$k]['branch'][0]);
//            }
        }
        print_r($list);
        
        $sum = db('order')
                ->where(['hospital_id' => 27,'pay_status' => 1,'status' => 3])
//                ->where(['hospital_fcbl'=>0])
//                ->where(['hospital_fcbl'=>['>',0]])
                ->sum('money');
        
        
//        $sum = db('branch')->where(['platform_id'=>15,'types'=>1])->sum('money');
        
        die('==' . $sum);
    }
}
