# 微信工具类使用说明

## 概述

`Wechat` 类是一个统一管理微信小程序、公众号、开放平台API调用的工具类，提供了access_token管理、手机号获取等功能。

## 主要特性

1. **多平台支持**：支持微信小程序、公众号、开放平台三种类型的API调用
2. **智能缓存**：使用ThinkPHP5的cache()方法自动管理access_token缓存
3. **错误处理**：完善的错误处理和日志记录机制
4. **安全性**：新版手机号获取方式，提高安全性

## 配置要求

在 `application/extra/wxali.php` 文件中配置相应的微信参数：

```php
return [
    'wx' => [
        'xcx' => [
            'appid' => 'your_mini_app_id',        // 小程序AppID
            'appsecret' => 'your_mini_app_secret', // 小程序AppSecret
        ],
        'mp' => [
            'appid' => 'your_mp_app_id',          // 公众号AppID（可选）
            'appsecret' => 'your_mp_app_secret',   // 公众号AppSecret（可选）
        ],
        'open' => [
            'appid' => 'your_open_app_id',        // 开放平台AppID（可选）
            'appsecret' => 'your_open_app_secret', // 开放平台AppSecret（可选）
        ],
    ]
];
```

## 使用方法

### 1. 基本使用

```php
use app\common\library\Wechat;

// 实例化微信工具类
$wechat = new Wechat();
```

### 2. 获取access_token

```php
// 获取微信小程序access_token
$accessToken = $wechat->getMiniAccessToken();

// 获取微信公众号access_token
$accessToken = $wechat->getMpAccessToken();

// 获取微信开放平台access_token
$accessToken = $wechat->getOpenAccessToken();
```

### 3. 获取用户手机号（新版方式）

```php
// 前端传递的动态令牌code
$code = $this->request->param('code');

// 获取手机号信息
$phoneInfo = $wechat->getMiniPhoneNumber($code);

if ($phoneInfo) {
    $mobile = $phoneInfo['purePhoneNumber']; // 纯手机号
    $fullMobile = $phoneInfo['phoneNumber']; // 完整手机号（含区号）
    $countryCode = $phoneInfo['countryCode']; // 区号
}
```

### 4. 缓存管理

```php
// 清除指定平台的access_token缓存
$wechat->clearAccessTokenCache('mini'); // 清除小程序缓存
$wechat->clearAccessTokenCache('mp');   // 清除公众号缓存
$wechat->clearAccessTokenCache('open'); // 清除开放平台缓存
```

## 前端配合使用

### 小程序端获取手机号（新版方式）

```javascript
// WXML
<button open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">
  获取手机号
</button>

// JS
getPhoneNumber(e) {
    if (!e.detail.code) {
        wx.showToast({
            title: '获取手机号失败',
            icon: 'none'
        });
        return;
    }
    
    // 发送code到后端
    wx.request({
        url: '/api/user/wxUpdateMobile',
        method: 'POST',
        data: {
            code: e.detail.code
        },
        success: function(res) {
            if (res.data.code === 1) {
                console.log('手机号获取成功：', res.data.data);
            }
        }
    });
}
```

## 错误处理

工具类会自动记录错误日志，可以通过ThinkPHP的日志系统查看：

- 成功日志：记录access_token获取成功、手机号获取成功等信息
- 错误日志：记录网络请求失败、API调用失败等错误信息

## 技术问题解答

### 问题1：关于access_token的获取和刷新机制

#### 重要澄清
**微信官方API中没有专门的access_token刷新接口**。所谓的"刷新"实际上就是重新调用获取access_token的接口。

#### access_token机制说明
1. **获取方式**：通过 `https://api.weixin.qq.com/cgi-bin/token` 接口获取
2. **有效期**：7200秒（2小时）
3. **限制**：每日调用次数有限制（通常为2000次）
4. **缓存策略**：建议缓存时间设置为7000秒，留出200秒缓冲时间

#### 何时需要"刷新"access_token
1. **缓存过期**：缓存中没有access_token时
2. **token失效**：调用API返回40001错误码时
3. **手动刷新**：开发调试或异常情况下的强制刷新

#### 自动重试机制
工具类实现了智能的自动重试机制：
- 当API调用返回40001错误时，自动强制刷新access_token并重试一次
- 防止无限递归，最多重试1次
- 详细的日志记录，便于问题排查

### 问题2：关于小程序中手机号获取的代码复用

#### 统一升级策略
所有手机号获取功能都已升级为新版方式：
1. **首页扫码租借**：已升级使用公共方法
2. **个人中心页面**：已升级使用公共方法
3. **其他页面**：如有类似功能，建议统一使用公共方法

#### 公共方法设计
在 `app.js` 中创建了 `getWechatPhoneNumber()` 公共方法：
- **统一处理**：所有手机号获取逻辑集中管理
- **回调机制**：支持成功和失败回调，便于不同页面的业务处理
- **错误处理**：统一的错误提示和日志记录
- **参数验证**：统一的参数检查和登录状态验证

#### 使用示例
```javascript
// 在页面中调用公共方法
app.getWechatPhoneNumber(e,
  // 成功回调
  function(mobile) {
    // 处理成功逻辑
    console.log('获取到手机号：', mobile);
  },
  // 失败回调
  function(errorMsg) {
    // 处理失败逻辑
    console.log('获取失败：', errorMsg);
  }
);
```

## 最佳实践建议

### 1. access_token管理
- **缓存优先**：优先从缓存获取，减少API调用次数
- **自动刷新**：实现自动检测失效并刷新的机制
- **错误处理**：完善的错误日志和异常处理
- **并发控制**：避免同时多次调用获取接口

### 2. 手机号获取
- **统一流程**：所有环境都使用微信官方标准流程
- **用户体验**：提供清晰的加载提示和错误反馈
- **代码复用**：使用公共方法避免重复代码
- **安全性**：使用新版code方式，提高安全性

### 3. 错误处理
- **分类处理**：区分网络错误、API错误、业务错误
- **用户友好**：提供用户可理解的错误提示
- **日志记录**：详细记录错误信息便于调试
- **统一标准**：所有环境使用相同的错误处理逻辑

## 注意事项

1. **配置安全**：确保AppSecret等敏感信息的安全，不要提交到版本控制系统
2. **缓存时间**：access_token缓存时间设置为7000秒，留出200秒缓冲时间
3. **网络超时**：HTTP请求超时时间设置为30秒
4. **错误重试**：如果access_token获取失败，可以调用refresh方法强制刷新
5. **并发安全**：在高并发场景下注意缓存的并发安全问题

## 版本兼容性

- **新版手机号获取**：适用于微信小程序基础库 >= 2.21.2
- **统一标准**：所有环境都使用微信官方推荐的新版方式
- **生产就绪**：代码已优化为生产环境标准

## 测试接口

提供了测试接口用于验证工具类功能：

```
GET /api/user/testWechatTool
```

该接口会返回配置信息和access_token获取状态，用于开发调试。
