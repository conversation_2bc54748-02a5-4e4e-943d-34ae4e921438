<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Platform_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-platform_id" data-rule="required" data-source="platform/index" class="form-control selectpage" name="row[platform_id]" type="text" value="{$row.platform_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_id" data-rule="required" data-source="agent/index" class="form-control selectpage" name="row[agent_id]" type="text" value="{$row.agent_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hospital_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hospital_id" data-rule="required" data-source="hospital/index" class="form-control selectpage" name="row[hospital_id]" type="text" value="{$row.hospital_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Departments_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-departments_id" data-rule="required" data-source="departments/index" class="form-control selectpage" name="row[departments_id]" type="text" value="{$row.departments_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Equipment_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-equipment_id" data-rule="required" data-source="equipment/index" class="form-control selectpage" name="row[equipment_id]" type="text" value="{$row.equipment_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Equipment_info_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-equipment_info_id" data-rule="required" data-source="equipment/info/index" class="form-control selectpage" name="row[equipment_info_id]" type="text" value="{$row.equipment_info_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Info')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-info" class="form-control" name="row[info]" type="text" value="{$row.info}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
