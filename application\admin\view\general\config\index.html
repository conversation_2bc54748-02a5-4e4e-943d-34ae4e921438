<style type="text/css">
    @media (max-width: 375px) {
        .edit-form tr td input{width:100%;}
        .edit-form tr th:first-child,.edit-form tr td:first-child{
            width:20%;
        }
        .edit-form tr th:nth-last-of-type(-n+2),.edit-form tr td:nth-last-of-type(-n+2){
            display: none;
        }
    }
    .edit-form table > tbody > tr td a.btn-delcfg{
        visibility: hidden;
    }
    .edit-form table > tbody > tr:hover td a.btn-delcfg{
        visibility: visible;
    }
</style>
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null, false)}
        <ul class="nav nav-tabs">
            {foreach $siteList as $index=>$vo} 
            <li class="{$vo.active?'active':''}"><a href="#{$vo.name}" data-toggle="tab">{:__($vo.title)}</a></li>
            {/foreach}
            <li>
                <a href="#addcfg" data-toggle="tab"><i class="fa fa-plus"></i></a>
            </li>
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            {foreach $siteList as $index=>$vo} 
            <div class="tab-pane fade {$vo.active ? 'active in' : ''}" id="{$vo.name}">
                <div class="widget-body no-padding">
                    <form id="{$vo.name}-form" class="edit-form form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('general.config/edit')}">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th width="15%">{:__('Title')}</th>
                                    <th width="68%">{:__('Value')}</th>
                                    <th width="15%">{:__('Name')}</th>
                                    <th width="2%"></th>
                                </tr>
                            </thead>
                            <tbody>
                                {foreach $vo.list as $item}
                                <tr>
                                    <td>{$item.title}</td>
                                    <td>
                                        <div class="row">
                                            <div class="col-sm-8 col-xs-12">
                                                {switch $item.type}
                                                {case string}
                                                <input {$item.extend} type="text" name="row[{$item.name}]" value="{$item.value}" class="form-control" data-rule="{$item.rule}" data-tip="{$item.tip}" />
                                                {/case}
                                                {case text}
                                                <textarea {$item.extend} name="row[{$item.name}]" class="form-control" data-rule="{$item.rule}" rows="5" data-tip="{$item.tip}">{$item.value}</textarea>
                                                {/case}
                                                {case editor}
                                                <textarea {$item.extend} name="row[{$item.name}]" id="editor-{$item.name}" class="form-control editor" data-rule="{$item.rule}" rows="5" data-tip="{$item.tip}">{$item.value}</textarea>
                                                {/case}
                                                {case array}
                                                <dl class="fieldlist" data-name="row[{$item.name}]">
                                                    <dd>
                                                        <ins>{:__('Array key')}</ins>
                                                        <ins>{:__('Array value')}</ins>
                                                    </dd>
                                                    <dd><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> {:__('Append')}</a></dd>
                                                    <textarea name="row[{$item.name}]" class="form-control hide" cols="30" rows="5">{$item.value}</textarea>
                                                </dl>
                                                {/case}
                                                {case datetime}
                                                <input {$item.extend} type="text" name="row[{$item.name}]" value="{$item.value}" class="form-control datetimepicker" data-tip="{$item.tip}" data-rule="{$item.rule}" />
                                                {/case}
                                                {case number}
                                                <input {$item.extend} type="number" name="row[{$item.name}]" value="{$item.value}" class="form-control" data-tip="{$item.tip}" data-rule="{$item.rule}" />
                                                {/case}
                                                {case checkbox}
                                                {foreach name="item.content" item="vo"}
                                                <label for="row[{$item.name}][]-{$key}"><input id="row[{$item.name}][]-{$key}" name="row[{$item.name}][]" type="checkbox" value="{$key}" data-tip="{$item.tip}" {in name="key" value="$item.value"}checked{/in} /> {$vo}</label>
                                                {/foreach}
                                                {/case}
                                                {case radio}
                                                {foreach name="item.content" item="vo"}
                                                <label for="row[{$item.name}]-{$key}"><input id="row[{$item.name}]-{$key}" name="row[{$item.name}]" type="radio" value="{$key}" data-tip="{$item.tip}" {in name="key" value="$item.value"}checked{/in} /> {$vo}</label>
                                                {/foreach}
                                                {/case}
                                                {case value="select" break="0"}{/case}
                                                {case value="selects"}
                                                <select {$item.extend} name="row[{$item.name}]{$item.type=='selects'?'[]':''}" class="form-control selectpicker" data-tip="{$item.tip}" {$item.type=='selects'?'multiple':''}>
                                                    {foreach name="item.content" item="vo"}
                                                    <option value="{$key}" {in name="key" value="$item.value"}selected{/in}>{$vo}</option>
                                                    {/foreach}
                                                </select>
                                                {/case}
                                                {case value="image" break="0"}{/case}
                                                {case value="images"}
                                                <div class="form-inline">
                                                    <input id="c-{$item.name}" class="form-control" size="50" name="row[{$item.name}]" type="text" value="{$item.value}" data-tip="{$item.tip}">
                                                    <span><button type="button" id="plupload-{$item.name}" class="btn btn-danger plupload" data-input-id="c-{$item.name}" data-mimetype="image/*" data-multiple="{$item.type=='image'?'false':'true'}" data-preview-id="p-{$item.name}"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                                    <span><button type="button" id="fachoose-{$item.name}" class="btn btn-primary fachoose" data-input-id="c-{$item.name}" data-mimetype="image/*" data-multiple="{$item.type=='image'?'false':'true'}"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                                    <span class="msg-box n-right" for="c-{$item.name}"></span>
                                                    <ul class="row list-inline plupload-preview" id="p-{$item.name}"></ul>
                                                </div>
                                                {/case}
                                                {case value="file" break="0"}{/case}
                                                {case value="files"}
                                                <div class="form-inline">
                                                    <input id="c-{$item.name}" class="form-control" size="50" name="row[{$item.name}]" type="text" value="{$item.value}" data-tip="{$item.tip}">
                                                    <span><button type="button" id="plupload-{$item.name}" class="btn btn-danger plupload" data-input-id="c-{$item.name}" data-multiple="{$item.type=='file'?'false':'true'}"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                                    <span><button type="button" id="fachoose-{$item.name}" class="btn btn-primary fachoose" data-input-id="c-{$item.name}" data-multiple="{$item.type=='file'?'false':'true'}"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                                    <span class="msg-box n-right" for="c-{$item.name}"></span>
                                                </div>
                                                {/case}
                                                {case switch}
                                                <input id="c-{$item.name}" name="row[{$item.name}]" type="hidden" value="{:$item.value?1:0}">
                                                <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-{$item.name}" data-yes="1" data-no="0" >
                                                    <i class="fa fa-toggle-on text-success {if !$item.value}fa-flip-horizontal text-gray{/if} fa-2x"></i>
                                                </a>
                                                {/case}
                                                {case bool}
                                                <label for="row[{$item.name}]-yes"><input id="row[{$item.name}]-yes" name="row[{$item.name}]" type="radio" value="1" {$item.value?'checked':''} data-tip="{$item.tip}" /> {:__('Yes')}</label> 
                                                <label for="row[{$item.name}]-no"><input id="row[{$item.name}]-no" name="row[{$item.name}]" type="radio" value="0" {$item.value?'':'checked'} data-tip="{$item.tip}" /> {:__('No')}</label>
                                                {/case}
                                                {/switch}
                                            </div>
                                            <div class="col-sm-4"></div>
                                        </div>

                                    </td>
                                    <td>{php}echo "{\$site.". $item['name'] . "}";{/php}</td>
                                    <td><a href="javascript:;" class="btn-delcfg text-muted" data-name="{$item.name}"><i class="fa fa-times"></i></a></td>
                                </tr>
                                {/foreach}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td></td>
                                    <td>
                                        <button type="submit" class="btn btn-success btn-embossed">{:__('OK')}</button>
                                        <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                                    </td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            </tfoot>
                        </table>
                    </form>
                </div>
            </div>
            {/foreach}
            <div class="tab-pane fade" id="addcfg">
                <form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('general.config/add')}">
                    <div class="form-group">
                        <label for="type" class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <select name="row[type]" class="form-control selectpicker">
                                {foreach name="typeList" item="vo"}
                                <option value="{$key}" {in name="key" value="string"}selected{/in}>{$vo}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="group" class="control-label col-xs-12 col-sm-2">{:__('Group')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <select name="row[group]" class="form-control selectpicker">
                                {foreach name="groupList" item="vo"}
                                <option value="{$key}" {in name="key" value="basic"}selected{/in}>{$vo}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="name" class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <input type="text" class="form-control" id="name" name="row[name]" value="" data-rule="required; length(3~30); remote(general/config/check)" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="title" class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <input type="text" class="form-control" id="title" name="row[title]" value="" data-rule="required" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="value" class="control-label col-xs-12 col-sm-2">{:__('Value')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <input type="text" class="form-control" id="value" name="row[value]" value="" data-rule="" />
                        </div>
                    </div>
                    <div class="form-group hide" id="add-content-container">
                        <label for="content" class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <textarea name="row[content]" id="content" cols="30" rows="5" class="form-control" data-rule="required">value1|title1
value2|title2</textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="tip" class="control-label col-xs-12 col-sm-2">{:__('Tip')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <input type="text" class="form-control" id="tip" name="row[tip]" value="" data-rule="" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="rule" class="control-label col-xs-12 col-sm-2">{:__('Rule')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <input type="text" class="form-control" id="rule" name="row[rule]" value="" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="extend" class="control-label col-xs-12 col-sm-2">{:__('Extend')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <textarea name="row[extend]" id="extend" cols="30" rows="5" class="form-control" data-rule=""></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2"></label>
                        <div class="col-xs-12 col-sm-4">
                            <button type="submit" class="btn btn-success btn-embossed">{:__('OK')}</button>
                            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                        </div>
                    </div>

                </form>

            </div>
        </div>
    </div>
</div>
