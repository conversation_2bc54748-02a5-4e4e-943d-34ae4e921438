<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use fast\Random;
use think\Validate;
use weixin\WXBizDataCrypt;

/**
 * 微信相关
 */
class Wechat extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 发送消息
     * @param string $orenid 会员openid
     * @param string $template_id 模板消息id
     * @param string $url 链接地址
     * @param array $data 发送数据
     */
    public function newsSendout($orenid,$template_id,$url,$data){
        $ACCESS_TOKEN = $this->getAccessToken();
        $post_url = 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token='.$ACCESS_TOKEN;
        
        $post = array(
            'touser' => $orenid,//接收人openID
            'template_id' => $template_id,//模板ID
            'page' => $url,//模板跳转链接
            'miniprogram_state' => 'trial',//跳转小程序类型：developer为开发版；trial为体验版；formal为正式版；默认为正式版
            'lang' => 'zh_CN',
            'data' => $data,
        );
        $postJson = json_encode($post);
        $return = curl_post($post_url, $postJson);
    }
    
     /**
     * 获得access_token
     * @return string token
     */
    public function getAccessToken() {
        $success_token = db('weixin_token')->where(['types' => 1, 'termtime' => ['>', time()]])->value('access_token');
        if (!$success_token) {
            $payconfig = config('wxali.wx');
            
            $url = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=' . $payconfig['xcx']['appid'] . '&secret=' . $payconfig['xcx']['appsecret'];
            $ret = json_decode(file_get_contents($url), true);
            if (!isset($ret['errcode'])) {
                $data = array(
                    'types' => 1,
                    'access_token' => $ret['access_token'],
                    'termtime' => time() + $ret['expires_in'],
                    'createtime' => time(),
                );
                db('weixin_token')->insert($data);
                $success_token = $ret['access_token'];
            }
        }
        return $success_token;
    }
}
