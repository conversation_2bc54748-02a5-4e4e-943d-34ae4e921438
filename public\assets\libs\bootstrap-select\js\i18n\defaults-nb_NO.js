/*
 * Translated default messages for bootstrap-select.
 * Locale: NB (Norwegian; Bokmål)
 * Region: NO (Norway)
 */
(function ($) {
  $.fn.selectpicker.defaults = {
    noneSelectedText: 'Ingen valgt',
    noneResultsText: 'Søket gir ingen treff {0}',
    countSelectedText: function (numSelected, numTotal) {
      return (numSelected == 1) ? "{0} alternativ valgt" : "{0} alternativer valgt";
    },
    maxOptionsText: function (numAll, numGroup) {
      return [
        (numAll == 1) ? '<PERSON><PERSON><PERSON> nådd (maks {n} valg)' : '<PERSON><PERSON><PERSON> nådd (maks {n} valg)',
        (numGroup == 1) ? '<PERSON><PERSON>se for grupper nådd (maks {n} grupper)' : '<PERSON><PERSON><PERSON> for grupper nådd (maks {n} grupper)'
      ];
    },
    selectAllText: 'Merk alle',
    deselectAllText: '<PERSON>jern alle',
    multipleSeparator: ', '
  };
})(jQuery);
