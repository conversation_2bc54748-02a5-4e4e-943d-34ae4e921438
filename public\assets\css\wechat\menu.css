.weixin-menu-setting{
    margin:0;
    margin-bottom:10px;
    width:100%;
}
.mobile-head-title{
    color: #fff;
    text-align: center;
    padding-top: 33px;
    font-size: 15px;
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: normal;
    margin: 0 40px 0 70px;
}
.weixin-body {
    padding:0;
    margin:0;
    margin-left:337px;
}
.weixin-content,.no-weixin-content{
    background-color: #f4f5f9;
    border: 1px solid #e7e7eb;
    padding:15px;
}
.no-weixin-content{
    border:#fff;
    background-color: #fff;
    vertical-align: middle;
    padding-top:200px;
    text-align: center;
}
@media (max-width: 720px) {
    .weixin-body {
        margin-left:0;
        margin-top:560px;
    }
}
.weixin-menu-title{
    border-bottom: 1px solid #e7e7eb;
    font-size: 16px;
    padding: 0 20px;
    line-height: 55px;
    margin-bottom: 20px;
}
.mobile-menu-preview{
    display:block;
    float:left;
    position:relative;
    width: 317px;
    height: 550px;
    background: transparent url(../../img/wx_mobile_header_bg.png) no-repeat 0 0;
    background-position: 0 0;
    border: 1px solid #e7e7eb;
}

.mobile-menu-preview .menu-list {
    position: absolute;
    height:50px;
    bottom: 0;
    left: 0;
    right: 0;
    border-top: 1px solid #e7e7eb;
    background: transparent url(../../img/wx_mobile_footer_bg.png) no-repeat 0 0;
    background-position: 0 0;
    background-repeat: no-repeat;
    padding-left: 43px;
    margin:0;
}
.menu-list .menu-item,.menu-list .add-item{
    line-height: 50px;
    position: relative;
    float: left;
    text-align: center;
    width: 33.33%;
    list-style: none;
}
.ui-sortable-placeholder{
    background-color:#fff;
}
.menu-item a,.add-item a{
    display: block;
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: normal;
    color: #616161;
    text-decoration: none;
}
.menu-item.current a.menu-link{
    border: 1px solid #44b549;
    line-height: 48px;
    background-color: #fff;
    color: #44b549;
}
.menu-item .icon-menu-dot{
    background: url(../../img/wx_mobile_index.png) 0 0 no-repeat;
    width: 7px;
    height: 7px;
    vertical-align: middle;
    display: inline-block;
    margin-right: 2px;
    margin-top: -2px;
    bottom: 60px;
    background-color: #fafafa;
    border-top-width: 0;
}
.menu-item .menu-link,.add-item .menu-link{
    border-left-width: 0;
    border-left: 1px solid #e7e7eb;
    text-align: center;
}

.sub-menu-item a,.add-sub-item a{
    border: 1px solid #d0d0d0;
    position:relative;
    padding:0 0.5em;
}
.sub-menu-item.current a{
    border: 1px solid #44b549;
    background-color: #fff;
    color: #44b549;
    z-index: 1;
}
.sub-menu-list li a:hover{
    background:#f1f1f1;
}
.menu-item.current .menu-link{
    border: 1px solid #44b549;
    line-height: 48px;
    background-color: #fff;
    color: #44b549;
}
.sub-menu-box{
    position: absolute;
    bottom: 60px;
    left: 0;
    width: 100%;
    background-color: #fff;
    border-top: none;
}
.sub-menu-list{
    line-height: 50px;
    margin:0;padding:0;
}
.sub-menu-list li{
    line-height: 44px;
    margin: -1px -1px 0;
    list-style: none;
}
.sub-menu-box .arrow {
    position: absolute;
    left: 50%;
    margin-left: -6px;
}

.sub-menu-box .arrow-in {
    bottom: -5px;
    display: inline-block;
    width: 0;
    height: 0;
    border-width: 6px;
    border-style: dashed;
    border-color: transparent;
    border-bottom-width: 0;
    border-top-color: #fafafa;
    border-top-style: solid;
}
.sub-menu-box .arrow-out {
    bottom: -6px;
    display: inline-block;
    width: 0;
    height: 0;
    border-width: 6px;
    border-style: dashed;
    border-color: transparent;
    border-bottom-width: 0;
    border-top-color: #d0d0d0;
    border-top-style: solid;
}
.sub-menu-item.current{

}
.sub-menu-inner-add{
    display: block;
    border-top: 1px solid #e7e7eb;
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: normal;
    cursor: pointer;
}
.weixin-icon{
    background: url(../../img/weixin_icon.png) 0 -4418px no-repeat;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    display: inline-block;
    line-height: 100px;
    overflow: hidden;
}
.weixin-icon.add-gray {
    background-position: 0 0;
}
.weixin-icon.sort-gray {
    background: url(../../img/weixin_icon.png) 0 -32px no-repeat;
    background-position: 0 -32px;
    margin-top: -1px;
    display:none;
    width: 20px;
}
.weixin-icon.big-add-gray{
    background-position: -36px 0;
    width: 36px;
    height: 36px;
    vertical-align: middle;
}
.menu-item a.menu-link:hover{

}

.add-item.extra,.add-item.extra{
    float: none;
    width: auto;
    overflow: hidden;
}

table.btn-bar{width:100%;}
table.btn-bar td{ text-align: center; }

.item-info .item-head{
    position:relative;
    padding: 0;
    border-bottom: 1px solid #e7e7eb;
}
.item-info .item-delete{
    position:absolute;
    top:0;
    right:0;
}

table.weixin-form td{
    vertical-align:middle;
    height:24px;
    line-height: 24px;
    padding: 8px 0;
}

#menu-content{
    background-color: #fff;
    padding: 16px 20px;
    border: 1px solid #e7e7eb;
}
.menu-content-tips{
    color: #8d8d8d;
    padding-bottom: 10px;
}

.form-item dl{
    position:relative;
    margin:10px 0;
}
.form-item dl dt{
    width:90px;
    height: 30px;
    line-height: 30px;
    text-align: right;
    position:absolute;
    vertical-align: middle;
    top:0;
    left:0;
    bottom:0;
    display:block;
}
.form-item dl dd{
    position:relative;
    display:block;
    margin-left: 90px;
    line-height: 30px;
}
.form-item .input-box {
    display: inline-block;
    position: relative;
    height: 30px;
    line-height: 30px;
    vertical-align: middle;
    width: 278px;
    font-size: 14px;
    padding: 0 10px;
    border: 1px solid #e7e7eb;
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    background-color: #fff;
}
.form-item .input-box input{
    width: 100%;
    background-color: transparent;
    border: 0;
    outline: 0;
    height:30px;
}

.clickbox{
    text-align: center;
    margin:40px 0;
}
.create-click{
    display: inline-block;
    padding-top: 30px;
    position: relative;
    width:240px;
    height: 120px;
    border: 2px dotted #d9dadc;
    text-align: center;
    margin-bottom: 20px;
    margin-left: 50px;
}
.create-click a{
    display:block;
}
.create-click a strong{
    display:block;
}

.keytitle {
    position:absolute;
    width:100%;
    text-align:center;
    top:0px;
    height:35px;
    line-height:35px;
    background:#f4f5f9;
}
dl.is-item dd>label {margin-left:5px;}