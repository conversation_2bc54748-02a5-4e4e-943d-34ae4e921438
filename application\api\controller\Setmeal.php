<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use fast\Random;
use think\Validate;
use weixin\WXBizDataCrypt;
use weixin\WeixinPay;
use think\Db;

/**
 * 套餐
 */
class Setmeal extends Api {

    protected $noNeedLogin = ['lists'];
    protected $noNeedRight = '*';

    public function _initialize() {
        parent::_initialize();
    }

    /*
     * 套餐列表
     */

    public function lists() {
        $hospital_id = $this->request->param('hospital_id', null); //物业id
        if (!$hospital_id) {
            $this->error('参数错误');
        }
        $hospital = Db::name('hospital')->where(['id' => $hospital_id])->find();
        if (!$hospital) {
            $this->error('物业不存在');
        }
        $list = Db::name('setmeal')->where(['agent_id' => $hospital['agent_id'], 'state' => 1, 'deletetime' => null])->order('price asc')->field('*')->select();
        if (!$list) {
            $this->error('暂无套餐');
        }
        $this->success('加载成功', $list);
    }

    /*
     * 购买套餐
     */

    public function pay() {
        $setmeal_id = $this->request->param('setmeal_id', null); //套餐id
        $hospital_id = $this->request->param('hospital_id', null); //物业id
        if (!$setmeal_id || !$hospital_id) {
            $this->error('参数错误');
        }
        $setmeal = Db::name('setmeal')->where(['id' => $setmeal_id])->find();
        if (!$setmeal) {
            $this->error('套餐不存在');
        }
        if ($setmeal['state'] == 2) {
            $this->error('套餐已下架');
        }
        $hospital = Db::name('hospital')->where(['id' => $hospital_id])->find();
        $log_data = [
            'setmeal_id' => $setmeal_id,
            'agent_id' => $hospital['agent_id'],
            'hospital_id' => $hospital['id'],
            'user_id' => $this->auth->id,
            'set_name' => $setmeal['name'],
            'createtime' => time(),
            'status' => 1,
        ];
        $res = db('setmeal_log')->insertGetId($log_data);
        if (!$res) {
            $this->error('购买记录生成失败');
        }
        //生成支付订单
        $pay_data = array(
            'sn' => $this->getOrdersn('pay'),
            'user_id' => $this->auth->id,
            'types' => 3,
            'status' => 1,
            'money' => $setmeal['price'],
            'setmeallog_id' => $res,
            'createtime' => time(),
            'updatetime' => time(),
        );
        $res = Db::name('pay')->insertGetId($pay_data);
        if ($res) {
            $order = new \app\api\controller\Order();
            $return = $order->payment($res);
            if ($return['success']) {
                $this->success('加载成功', $return['data']);
            } else {
                $this->error($return['msg']);
            }
        } else {
            $this->error('支付记录创建失败');
        }
    }

    //随机生成订单编号
    private function getOrdersn($surface = 'order') {
        $no = 'ord' . date('YmdHis') . rand(10000000, 99999999);
        if (db($surface)->where('sn', $no)->find()) {
            $no = $this->getOrdersn();
        } else {
            return $no;
        }
    }

    /**
     * 我的套餐列表
     */
    public function mylists() {
        $page = $this->request->param('page', 1); //页码
        $limit = $this->request->param('limit', 10); //每页查询数量
        $state = $this->request->param('state', 1); //状态 1 使用中  2 已过期

        $order = 'id deac';
        $field = 'log.*,hos.name as hos_name,p.money as pay_money';
        $where = [
            'log.status' => 2,
            'log.deletetime' => null,
            'log.user_id' => $this->auth->id,
        ];
        if($state == 1){
            $where['effective_end'] = ['>',  time()];
        }  else {
            $where['effective_end'] = ['<',  time()];
        }

        $list_obj = Db::name('setmeal_log')//套餐购买记录表
                ->alias('log')
                ->join('setmeal set', 'set.id = log.setmeal_id')//套餐表
                ->join('hospital hos', 'hos.id = log.hospital_id')//门店表
                ->join('pay p', 'log.id = p.setmeallog_id')//支付表
                ->field($field)
                ->where($where)
                ->order($order)
                ->paginate(array('list_rows' => $limit, 'page' => $page))
                ->toArray();
        $list = $list_obj['data'];
        foreach ($list as $k => $v) {
            $list[$k]['effective_start'] = date('Y-m-d',$list[$k]['effective_start']);
            $list[$k]['effective_end'] = date('Y-m-d',$list[$k]['effective_end']);
        }
        $total = Db::name('setmeal_log')->alias('log')->join('setmeal set', 'set.id = log.setmeal_id')->join('hospital hos', 'hos.id = log.hospital_id')->join('pay p', 'log.id = p.setmeallog_id')->where($where)->count();
        $data['nextpage'] = ceil($total / $limit) > $page ? true : false;
        $data['list'] = $list;
        $this->success('加载成功', $data);
    }

}
