/*!
 * Bootstrap-select v1.11.2 (http://silviomoreto.github.io/bootstrap-select)
 *
 * Copyright 2013-2016 bootstrap-select
 * Licensed under MIT (https://github.com/silviomoreto/bootstrap-select/blob/master/LICENSE)
 */
!function(a,b){"function"==typeof define&&define.amd?define(["jquery"],function(a){return b(a)}):"object"==typeof exports?module.exports=b(require("jquery")):b(jQuery)}(this,function(a){!function(a){a.fn.selectpicker.defaults={noneSelectedText:"Ei valintoja",noneResultsText:"Ei hakutuloksia {0}",countSelectedText:function(a,b){return 1==a?"{0} valittu":"{0} valitut"},maxOptionsText:function(a,b){return["<PERSON><PERSON><PERSON><PERSON><PERSON> maksimimäär<PERSON> ({n} saavutettu)","<PERSON><PERSON><PERSON><PERSON><PERSON> maksimim<PERSON><PERSON><PERSON><PERSON> ({n} saavutettu)"]},selectAllText:"Valitse kaikki",deselectAllText:"Poista kaikki",multipleSeparator:", "}}(a)});