<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use fast\Random;
use think\Validate;
use think\Db;
use cabinet\Cabinet as cab;
use think\Log;
use app\api\controller\Ajax;
use app\api\controller\order\Order as ord;

/**
 * 共享床头柜
 */
class Cabinet extends Api {

    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];
    protected $appId = '';
    protected $secret = '';
    protected $token = '';
    protected $cad = null;

    public function _initialize() {
        $config = config('cabinet.hardware');
        $this->appId = $config['appId'];
        $this->secret = $config['secret'];
        $this->cad = new cab($this->appId, $this->secret);
        $this->token = $this->accessToken();
        parent::_initialize();
    }

    /**
     * 验证设备是否正在被租用开锁过程中
     */
    public function scanCodeVerification($kpl) {
        $time = time();
        $res = Db::name('hardware_cabinet_code')->where(['status' => ['in', '1,2,3'], 'endtime' => ['>=', $time], 'kpl' => $kpl])->find();
        if ($res) {
            return false;
        }
        return true;
    }

    /**
     * 生成设备扫码记录
     */
    public function scanCode() {
        $kpl = input('kpl', null); //设备唯一编码
        $aj = new Ajax();
        $return = $aj->useVerification($kpl);
        if (!$return['success']) {
            $this->error($return['msg']);
        }
        $data = $return['data'];
        $equipment_info = db('equipment_info')->where(['id' => $data['id']])->find();
        $equipment = db('equipment')->where(['id' => $equipment_info['equipment_id']])->find();
        $endtime = time() + 180; //记录有效期 180秒
        $code_data = [
            'user_id' => $this->auth->id,
            'equipment_id' => $equipment['id'],
            'equipment_info_id' => $equipment_info['id'],
            'nb_number' => $equipment_info['nb_number'],
            'kpl' => $equipment_info['kpl'],
            'endtime' => $endtime,
            'user_type' => 1,
            'status' => 1,
            'createtime' => time(),
        ];
        $code_id = db('hardware_cabinet_code')->insertGetId($code_data);
        if (!$code_id) {
            $this->error('扫码记录生成失败');
        }
        $callback_url = '/api/cabinet/news';
        $token = $this->token;
//        echo($equipment['nb_device_id']);
//        echo '<br>';
//        echo $callback_url;
//        echo '<br>';
        $return = $this->cad->Unlocking($equipment['nb_device_id'], 'VehicleDetectorBasic', $callback_url, $token, $equipment_info['nb_number']);
        $return = json_decode($return, true);
//        print_r($return);die;
        if (isset($return['error_code'])) {//指令发送失败
            $code_update_data = [
                'status' => 5,
                'discard_type' => 1,
            ];
            db('hardware_cabinet_code')->where(['id' => $code_id])->update($code_update_data);
            $this->error('服务器指令下发失败');
        }
        $code_update_data = [
            'command_id' => $return['commandId'],
            'status' => 2,
        ];
        db('hardware_cabinet_code')->where(['id' => $code_id])->update($code_update_data);
        $this->success('扫码记录生成成功', [
            'code_id' => $code_id,
        ]);
    }

    /**
     * 查询扫码记录
     */
    public function scanInfo() {
        $code_id = input('code_id', null); //扫码记录id
        if (!$code_id) {
            $this->error('参数错误');
        }
        $code = db('hardware_cabinet_code')->where(['id' => $code_id])->find();
        if (!$code) {
            $this->error('扫码记录不存在');
        }
        $this->success('', $code);
    }

    /**
     * 取消扫码记录
     */
    public function scanCancel() {
        $code_id = input('code_id', null); //扫码记录id
        if (!$code_id) {
            $this->error('参数错误');
        }
        $code = db('hardware_cabinet_code')->where(['id' => $code_id])->find();
        if (!$code) {
            $this->error('扫码记录不存在');
        }
        if ($code['status'] == 1) {
            db('hardware_cabinet_code')->where(['id' => $code_id])->update([
                'status' => 4,
            ]);
        }
        $this->success('', $code);
    }

    /**
     * 设置回调地址
     */
    public function subscript() {
        $callback_url = '/api/cabinet/news';
        $return = $this->cad->subscript($callback_url, $this->token);
        print_r($return);
        die();
    }

    /**
     * 获得token
     */
    function accessToken() {
        $cabinet_token = Db::name('hardware_cabinet_token')->where(['expiresin' => ['>', time()]])->order('id desc')->find();
        if ($cabinet_token) {
            return $cabinet_token['token'];
        } else {
            $return = $this->cad->getToken();
            if ($return['code'] == 1) {//获得token成功
                $expiresin = time() + $return['data']['expiresIn'];
                $data = [
                    'token' => $return['data']['accessToken'],
                    'type' => $return['data']['tokenType'],
                    'refresh' => $return['data']['refreshToken'],
                    'expiresin' => $expiresin,
                    'scope' => $return['data']['scope'],
                    'createtime' => time(),
                ];
                $res = Db::name('hardware_cabinet_token')->insertGetId($data);
                return $return['data']['accessToken'];
            } else {//获得token失败
                return false;
            }
        }
    }

    /**
     * 硬件上报消息
     */
    public function news() {
        Log::write('==============================收到消息==============================', 'notice');
        $param = input('param.');
        Log::write(['param' => json_encode($param)], 'notice');
//        return true;
//        $param = '{"notifyType":"deviceDataChanged","deviceId":"df516171-90ea-4c43-91e4-afb41bd5f865","gatewayId":"df516171-90ea-4c43-91e4-afb41bd5f865","requestId":"","service":{"serviceId":"VehicleDetectorBasic","serviceType":"VehicleDetectorBasic","data":{"sequence":"5","status":"K","temperature":"1","signalStrength":"18","NO":"2021888042209","timestamp":"D001000000000000"},"eventTime":"20220618T014205Z"}}';
//        $param = json_decode($param,true);
//        print_r($param);
        $deviceId = $param['deviceId']; //设备唯一编号
        $equipment = db('equipment')->where(['nb_device_id' => $deviceId])->find(); //查询主设备信息
        $nbnew = [
            'datas' => json_encode($param),
            'date' => getMsecToMescdate(msectime()),
        ];
        if ($equipment) {
            $nbnew['equipment_id'] = $equipment['id'];
        }
        if ($equipment) {
            if (isset($param['commandId'])) {//指令下发进度通知类消息
                $nbnew['type_str'] = '指令下发进度通知类';
                $code = db('hardware_cabinet_code')->where(['command_id' => $param['commandId'], 'equipment_id' => $equipment['id']])->find();
                if ($code) {
                    $nbnew['code_id'] = $code['id'];
                    if ($param['result']['resultCode'] == 'EXPIRED' || $param['result']['resultCode'] == 'TIMEOUT') {//指令下发超时 ： 指令从物联网向设备下发超时
                        $nbnew['result'] = '指令下发超时';
                        $code_update_data = [
                            'status' => 5,
                            'discard_type' => 2,
                        ];
                        db('hardware_cabinet_code')->where(['id' => $code['id']])->update($code_update_data);
                    } else if ($param['result']['resultCode'] == 'CANCELED') {//命令已经被撤销执行 
                        $nbnew['result'] = '指令被撤销';
                        $code_update_data = [
                            'status' => 5,
                            'discard_type' => 3,
                        ];
                        db('hardware_cabinet_code')->where(['id' => $code['id']])->update($code_update_data);
                    } else if ($param['result']['resultCode'] == 'DELIVERED') {//命令已送达设备
                        $nbnew['result'] = '指令已送达设备';
                        $code_update_data = [
                            'status' => 3,
                        ];
                        db('hardware_cabinet_code')->where(['id' => $code['id']])->update($code_update_data);
                    }
                }
            } else {
                $notifyType = $param['notifyType']; //通知类型 ： deviceDataChanged 设备数据变化通知
                //根据设备为编号查询设备信息【未实现】
                if ($notifyType == 'deviceDataChanged') {//设备数据变化通知                    
                    $service = $param['service']; //设备的service数据
                    $nbnew['value'] = json_encode($service['data']);
                    $serviceId = $service['serviceId'];
                    $serviceType = $service['serviceType']; //消息类型：VehicleDetectorBasic 锁状态发生变化 Battery 锁电量
                    //根据序列号验证是否是最新消息【未实现】
                    if ($serviceType == 'VehicleDetectorBasic') {//锁状态发生变化
                        $nbnew['type_str'] = '锁状态发生变化';
                        $status = $service['data']['status']; //设备状态：0’表示锁处在打开状态，‘1’表示锁处在关闭状态。’R’表示锁复位了。‘K’表示按键触发上报。
                        $Temperature = $service['data']['temperature']; //开锁 or 还床类型
                        $position = (int) substr($service['data']['timestamp'], 1, 3); //锁位
                        $goodsId = substr($service['data']['timestamp'], 4, 12); //共享物品Id
                        $sequence = $service['data']['sequence']; //消息序列号：设备每次上报一条数据时序列号都会自动加1。当序列号增加到65535或者设备复位时，序列号从1开始。最小值1，最大值65535。注：应用服务器对设备状态的更新请务必以序列号为准。
                        $is = true;
                        if ($sequence != 1) {
                            if ($sequence <= $equipment['sequence']) {
                                $is = false;
                            }
                        }
                        
                        $nbnew['sequence'] = $sequence;
                        if ($is) {
                            db('equipment')->where(['id' => $equipment['id']])->update(['sequence' => $sequence]); //更新最新序列号
                            $equipment_info = db('equipment_info')->where(['equipment_id' => $equipment['id'], 'nb_number' => $position])->find();
                            if ($equipment_info) {
                                $nbnew['equipment_info_id'] = $equipment_info['id'];
                                if ($status == '0') {//表示锁处在打开状态
                                    $nbnew['result'] = '锁处于打开状态';
                                    $equipment_info_update_data = array('status' => 2,);
                                    //更新设备信息为 = 未租用
                                    db('equipment_info')->where(['id' => $equipment_info['id']])->update($equipment_info_update_data);
                                    $time = time();
                                    $code = db('hardware_cabinet_code')->where(['equipment_id' => $equipment['id'], 'equipment_info_id' => $equipment_info['id'], 'status' => 3, 'endtime' => ['>=', $time]])->order('id desc')->find(); //查询扫码记录，没有过期的扫码记录
                                    if ($code) {//有扫码记录，生成订单
                                        $ord = new ord();
                                        $return = $ord->orderAdd($equipment_info['id'], $code['user_id'], $goodsId);
                                        db('hardware_cabinet_code')->where(['id' => $code['id']])->update(['status' => 4]); //更新扫码记录状态为：已开锁
                                        $nbnew['code_id'] = $code['id'];
                                        if($return['success']){
                                            $nbnew['order_id'] = $return['data'];
                                        }                                        
                                    }
                                } else if ($status == '1') {//表示锁处在关闭状态
                                    $nbnew['result'] = '锁处于关闭状态';
                                    $equipment_info_update_data = array('status' => 1,);
                                    //更新设备信息为 = 未租用
                                    db('equipment_info')->where(['id' => $equipment_info['id']])->update($equipment_info_update_data);
                                    $order = db('order')->where(['status' => 1, 'nb_goodsid' => $goodsId])->find();
                                    if ($order) {
                                        $ord = new ord();
                                        $return = $ord->orderEnd($order);
                                        $nbnew['order_id'] = $order['id'];
                                    }
                                } else if ($status == 'R') {//表示锁复位了
                                    $nbnew['result'] = '锁复位';
                                } else if ($status == 'K') {//表示按键触发上报     
                                    $nbnew['result'] = '按键触发上报';
                                }
                            }
                        }
                    } else if ($serviceType == 'Battery') {//锁电量
                        $nbnew['type_str'] = '锁电量';
                        $batteryLevel = $service['data']['batteryLevel']; //电量
                        $data = array(
                            'voltage' => $batteryLevel,
                            'voltagetime' => time(),
                        );
                        $res = db('equipment')->where(['id' => $equipment['id']])->update($data);
                    }
                }
            }
        }
        db('equipmemt_nbnew')->insertGetId($nbnew);
    }

}
