<!DOCTYPE html>
<html>

    <head>

        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="description" content="">
        <meta name="author" content="">

        <title>FastAdmin - {:__('The fastest framework based on ThinkPHP5 and Bootstrap')}</title>

        <!-- Bootstrap Core CSS -->
        <link href="https://cdn.staticfile.org/twitter-bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
        <link href="__CDN__/assets/css/index.css" rel="stylesheet">

        <!-- Plugin CSS -->
        <link href="https://cdn.staticfile.org/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
        <link href="https://cdn.staticfile.org/simple-line-icons/2.4.1/css/simple-line-icons.min.css" rel="stylesheet">

        <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
        <!--[if lt IE 9]>
            <script src="https://cdn.staticfile.org/html5shiv/3.7.3/html5shiv.min.js"></script>
            <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
        <![endif]-->
    </head>

    <body id="page-top">

        <nav id="mainNav" class="navbar navbar-default navbar-fixed-top">
            <div class="container">
                <div class="navbar-header">
                    <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar-collapse-menu">
                        <span class="sr-only">Toggle navigation</span><i class="fa fa-bars"></i>
                    </button>
                    <a class="navbar-brand page-scroll" href="#page-top"><img src="__CDN__/assets/img/logo.png" style="width:200px;" alt=""></a>
                </div>

                <div class="collapse navbar-collapse" id="navbar-collapse-menu">
                    <ul class="nav navbar-nav navbar-right">
                        <li><a href="https://www.fastadmin.net" target="_blank">{:__('Home')}</a></li>
                        <li><a href="https://www.fastadmin.net/store.html" target="_blank">{:__('Store')}</a></li>
                        <li><a href="https://www.fastadmin.net/wxapp.html" target="_blank">{:__('Wxapp')}</a></li>
                        <li><a href="https://www.fastadmin.net/service.html" target="_blank">{:__('Services')}</a></li>
                        <li><a href="https://www.fastadmin.net/download.html" target="_blank">{:__('Download')}</a></li>
                        <li><a href="https://www.fastadmin.net/demo.html" target="_blank">{:__('Demo')}</a></li>
                        <li><a href="https://www.fastadmin.net/donate.html" target="_blank">{:__('Donation')}</a></li>
                        <li><a href="https://forum.fastadmin.net" target="_blank">{:__('Forum')}</a></li>
                        <li><a href="https://doc.fastadmin.net" target="_blank">{:__('Docs')}</a></li>
                    </ul>
                </div>
                <!-- /.navbar-collapse -->
            </div>
            <!-- /.container-fluid -->
        </nav>

        <header>
            <div class="container">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="header-content">
                            <div class="header-content-inner">
                                <h1>FastAdmin</h1>
                                <h3>{:__('The fastest framework based on ThinkPHP5 and Bootstrap')}</h3>
                                <a href="{:url('admin/index/login')}" class="btn btn-warning btn-xl page-scroll">{:__('Go to Dashboard')}</a>
                                <a href="{:url('index/user/index')}" class="btn btn-outline btn-xl page-scroll">{:__('Go to Member center')}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <section id="features" class="features">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12 text-center">
                        <div class="section-heading">
                            <h2>{:__('Features')}</h2>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="container-fluid">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="feature-item">
                                        <i class="icon-user text-primary"></i>
                                        <h3>{:__('Auth')}</h3>
                                        <p class="text-muted">{:__('Auth tips')}</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="feature-item">
                                        <i class="icon-screen-smartphone text-primary"></i>
                                        <h3>{:__('Responsive')}</h3>
                                        <p class="text-muted">{:__('Responsive tips')}</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="feature-item">
                                        <i class="icon-present text-primary"></i>
                                        <h3>{:__('Languages')}</h3>
                                        <p class="text-muted">{:__('Languages tips')}</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="feature-item">
                                        <i class="icon-layers text-primary"></i>
                                        <h3>{:__('Module')}</h3>
                                        <p class="text-muted">{:__('Module tips')}</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="feature-item">
                                        <i class="icon-docs text-primary"></i>
                                        <h3>{:__('CRUD')}</h3>
                                        <p class="text-muted">{:__('CRUD tips')}</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="feature-item">
                                        <i class="icon-puzzle text-primary"></i>
                                        <h3>{:__('Extension')}</h3>
                                        <p class="text-muted">{:__('Extension tips')}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="cta">
            <div class="cta-content">
                <div class="container">
                    <h2>{:__('Do not hesitate')}<br>{:__('Start to act')}</h2>
                    <a href="https://doc.fastadmin.net/docs/contributing.html" class="btn btn-outline btn-xl page-scroll">{:__('Contribution')}</a>
                </div>
            </div>
            <div class="overlay"></div>
        </section>

        <footer>
            <div class="container">
                <!-- FastAdmin是开源程序，建议在您的网站底部保留一个FastAdmin的链接 -->
                <p>&copy; 2017-2018 <a href="https://www.fastadmin.net" target="_blank">FastAdmin</a>. All Rights Reserved.</p>
                <ul class="list-inline">
                    <li>
                        <a href="https://gitee.com/karson/fastadmin">{:__('Gitee')}</a>
                    </li>
                    <li>
                        <a href="https://github.com/karsonzhang/fastadmin">{:__('Github')}</a>
                    </li>
                    <li>
                        <a href="https://shang.qq.com/wpa/qunwpa?idkey=46c326e570d0f97cfae1f8257ae82322192ec8841c79b2136446df0b3b62028c">{:__('QQ group')}</a>
                    </li>
                </ul>
            </div>
        </footer>

        <!-- jQuery -->
        <script src=https://cdn.staticfile.org/jquery/2.1.4/jquery.min.js></script>

        <!-- Bootstrap Core JavaScript -->
        <script src="https://cdn.staticfile.org/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>

        <!-- Plugin JavaScript -->
        <script src="https://cdn.staticfile.org/jquery-easing/1.4.1/jquery.easing.min.js"></script>

        <script>
            $(function () {
                $(window).on("scroll", function () {
                    $("#mainNav").toggleClass("affix", $(window).height() - $(window).scrollTop() <= 50);
                });

                // 发送版本统计信息，请移除
                try {
                    var installed = localStorage.getItem("installed");
                    if (!installed) {
                        $.ajax({
                            url: "{$Think.config.fastadmin.api_url}/statistics/installed",
                            data: {
                                version: "{:config('fastadmin.version')}",
                                os: "{$Think.PHP_OS}",
                                sapi: "{$Think.PHP_SAPI}",
                                tpversion: "{$Think.THINK_VERSION}",
                                phpversion: "{$Think.PHP_VERSION}",
                                software: "{$Request.server.SERVER_SOFTWARE}",
                                url: location.href,
                            },
                            dataType: 'jsonp',
                        });
                        localStorage.setItem("installed", true);
                    }
                } catch (e) {

                }

            });
        </script>

        <script>
            // FastAdmin统计代码，请移除
            var _hmt = _hmt || [];
            (function () {
                var hm = document.createElement("script");
                hm.src = "https://hm.baidu.com/hm.js?f8d0a8c400404989e195270b0bbf060a";
                var s = document.getElementsByTagName("script")[0];
                s.parentNode.insertBefore(hm, s);
            })();
        </script>

    </body>

</html>