<!DOCTYPE html>
<html lang="en">
    <head>
        {include file="common/meta" /}

        <style type="text/css">
            body {
                color:#999;
                background:url('{$background}');
                background-size:cover;
            }
            a {
                color:#fff;
            }
            .login-panel{margin-top:150px;}
            .login-screen {
                max-width:400px;
                padding:0;
                margin:100px auto 0 auto;

            }
            .login-screen .well {
                border-radius: 3px;
                -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                background: rgba(255,255,255, 0.2);
            }
            .login-screen .copyright {
                text-align: center;
            }
            @media(max-width:767px) {
                .login-screen {
                    padding:0 20px;
                }
            }
            .profile-img-card {
                width: 100px;
                height: 100px;
                margin: 10px auto;
                display: block;
                -moz-border-radius: 50%;
                -webkit-border-radius: 50%;
                border-radius: 50%;
            }
            .profile-name-card {
                text-align: center;
            }

            #login-form {
                margin-top:20px;
            }
            #login-form .input-group {
                margin-bottom:15px;
            }

        </style>
    </head>
    <body>
        <div class="container">
            <div class="login-wrapper">
                <div class="login-screen">
                    <div class="well">
                        <div class="login-form">
                            <img id="profile-img" class="profile-img-card" src="__CDN__/assets/img/avatar.png" />
                            <p id="profile-name" class="profile-name-card"></p>

                            <form action="" method="post" id="login-form">
                                <div id="errtips" class="hide"></div>
                                {:token()}
                                <div class="input-group">
                                    <div class="input-group-addon"><span class="glyphicon glyphicon-user" aria-hidden="true"></span></div>
                                    <input type="text" class="form-control" id="pd-form-username" placeholder="{:__('Username')}" name="username" autocomplete="off" value="" data-rule="{:__('Username')}:required;username" />
                                </div>

                                <div class="input-group">
                                    <div class="input-group-addon"><span class="glyphicon glyphicon-lock" aria-hidden="true"></span></div>
                                    <input type="password" class="form-control" id="pd-form-password" placeholder="{:__('Password')}" name="password" autocomplete="off" value="" data-rule="{:__('Password')}:required;" />
                                    <!--password-->
                                </div>
                                {if $config.fastadmin.login_captcha}
                                <div class="input-group">
                                    <div class="input-group-addon"><span class="glyphicon glyphicon-option-horizontal" aria-hidden="true"></span></div>
                                    <input type="text" name="captcha" class="form-control" placeholder="{:__('Captcha')}" data-rule="{:__('Captcha')}:required;length(4)" />
                                    <span class="input-group-addon" style="padding:0;border:none;cursor:pointer;">
                                        <img src="{:rtrim('__PUBLIC__', '/')}/index.php?s=/captcha" width="100" height="30" onclick="this.src = '{:rtrim('__PUBLIC__', '/')}/index.php?s=/captcha&r=' + Math.random();"/>
                                    </span>
                                </div>
                                {/if}
                                <div class="form-group">
                                    <label class="inline" for="keeplogin">
                                        <input type="checkbox" name="keeplogin" id="keeplogin" value="1" />
                                        {:__('Keep login')}
                                    </label>
                                </div>
                                <div class="form-group">
                                    <button type="submit" class="btn btn-success btn-lg btn-block">{:__('Sign in')}</button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <!-- FastAdmin是开源程序，建议在您的网站底部保留一个FastAdmin的链接 -->
                    <p class="copyright"><a href="http://www.juhuiwangluo.com">Powered By 聚辉网络</a>
                    <br>
                    <a href="https://beian.miit.gov.cn/#/Integrated/index">闽ICP备2022016939号-1</a>
                    </p>
                </div>
            </div>
        </div>
        {include file="common/script" /}
    </body>
</html>