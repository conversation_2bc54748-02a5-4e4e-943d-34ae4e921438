/*
 * Translated default messages for bootstrap-select.
 * Locale: FI (Finnish)
 * Region: FI (Finland)
 */
(function ($) {
  $.fn.selectpicker.defaults = {
    noneSelectedText: 'Ei valintoja',
    noneResultsText: '<PERSON>i hakutu<PERSON>ia {0}',
    countSelectedText: function (numSelected, numTotal) {
      return (numSelected == 1) ? "{0} valittu" : "{0} valitut";
    },
    maxOptionsText: function (numAll, numGroup) {
      return [
        (numAll == 1) ? 'Valintojen maksimimäärä ({n} saavutettu)' : 'Valintojen maksimimäärä ({n} saavutettu)',
        (numGroup == 1) ? '<PERSON><PERSON>hmän maksimimäär<PERSON> ({n} saavutettu)' : '<PERSON>yhmän maksimimäärä ({n} saavutettu)'
      ];
    },
    selectAllText: '<PERSON><PERSON><PERSON> kaikki',
    deselectAllText: 'Poista kaikki',
    multipleSeparator: ', '
  };
})(jQuery);
