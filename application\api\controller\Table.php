<?php

namespace app\api\controller;

use app\common\controller\Api;
use fast\Random;
use think\Validate;


/**
 * 老王测试接口
 */
class Table extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /*
     *  中国电信网关交流群 ： 939057654
     *  客服电话：           4008285656
     * */

    /**
     *  登录鉴权 -2.1.1 鉴权
     *
     *  功能：获取accessToken
     */
    public function login()
    {
        $url  = 'https://device.api.ct10649.com:8743/iocm/app/sec/v1.1.0/login';
        $data = [
            'appId'  => '7cef48841c56419b9e6bc4cbd3a0e719',
            'secret' => '40220bbfadf04fadbd96824b7a222119',
        ];
        // x-www-form-urlencoded时的数据则要变为 key=value&key=value的格式 ，类型是 string; $content_type=1
        $type    = 1;
        $result  = $this -> curlPost($url, $data, $type);
        $result  = json_decode($result, true);

       return json(['code'=>1, 'msg'=>'ok', 'data'=>$result]);
    }

    /*
     *  根据refreshToken重新获取accesstoken -2.1.2 刷新 token
     *  @param string $appId   参数
     *  @param string $secret  参数
     *  @param string $refreshToken用来重获accessToken； 有效期1月
     * */
    public function refreshToken(){
        $url  = 'https://device.api.ct10649.com:8743/iocm/app/sec/v1.1.0/refreshToken';
        $data = [
            'appId'        => '7cef48841c56419b9e6bc4cbd3a0e719',
            'secret'       => '40220bbfadf04fadbd96824b7a222119',
            'refreshToken' => '518d64a44c384120937637855cf56708',  // todo login接口返回的， 最好cookie缓存起来，后续好取
        ];
        //  application/json
        $type    = 2;
        $result  = $this -> curlPost($url, $data, $type);
        $result  = json_decode($result, true);

        // todo 接口返回refreshToken后， cookie更新一下，后续好取

        return json(['code'=>1, 'msg'=>'ok', 'data'=>$result]);
    }

    /*
     *  注册设备
     *  @param string $appId        参数
     *  @param string $verifyCode   设备验证码
     *  @param string $nodeId       设备的唯一标识
     *  @param string $endUserId    终端用户ID
     *  @param string $timeout      超时时间 0=长期
     * */
    public function deviceCredentials(){
        $appId      = '7cef48841c56419b9e6bc4cbd3a0e719';
        $url        = 'https://device.api.ct10649.com:8743/iocm/app/reg/v1.1.0/deviceCredentials?appId='.$appId;
        $verifyCode = '111';
        $nodeId     = '111';
        $endUserId  = '1';
        $timeout    = 0;
        $Token      = '3b4b445cc185430ca3e5345ef8761856';
        $data  = [
            'appId'       => $appId,
            'verifyCode'  => $verifyCode,
            'nodeId'      => $nodeId,
            'endUserId'   => $endUserId,
            'timeout'     => $timeout,
            'accessToken' => $Token,
        ];
        //  application/json
        $type    = 3;
        $result  = $this -> curlPost($url, $data, $type);
        $result  = json_decode($result, true);
        return json(['code'=>1, 'msg'=>'ok', 'data'=>$result]);
    }

    /*
     *  订阅 -2.5.1订阅平台业务数据
     *  备注： 当物联网平台中设备的业务信息发生变化时（如设备注册、设备数据上报、设备状态变更等），平台会向第三方应用发送通知消息，通知其具体的变化信息
     *
     * @param string $notifyType   通知类型
     * @param string $callbackUrl  订阅的回调地址，用于接收对应类型通知消息
     * @param string $appId        appId
     * @param string $ownerFlag    callbackUrl 的所有者标识
     */
    public function subscript(){
        $http_type   = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
        $appId       = '7cef48841c56419b9e6bc4cbd3a0e719';
        $url         = 'https://device.api.ct10649.com:8743/iocm/app/sub/v1.2.0/subscriptions';
        $notifyType  = 'deviceDataChanged';
        $callbackUrl = $http_type . $_SERVER['SERVER_NAME'] . '/index.php/api/table/subscriptCallback';
        $ownerFlag   = false;
        $Token       = '3b4b445cc185430ca3e5345ef8761856';
        $data  = [
            'appId'       => $appId,
            'notifyType'  => $notifyType,
            'callbackUrl' => $callbackUrl,
            'ownerFlag'   => $ownerFlag,
            'accessToken' => $Token,
        ];
        //  application/json
        $type    = 3;
        $result  = $this -> curlPost($url, $data, $type);
        $result  = json_decode($result, true);
        return json(['code'=>1, 'msg'=>'ok', 'data'=>$result]);
    }

    // 订阅回调
    public function subscriptCallback(){
        $param = input('param.');
        trace('----------------订阅回调---------------', 'error');
        trace($param, 'error');

        $deviceId   = $param['deviceId'];
        $service    = $param['service'];
        $serviceId  = $service['serviceId'];
        if($serviceId != 'Battery'){ // todo 不是电池，即开门，也可能存在其它情况， 电池不是唯一判断标准
            $this -> Unlocking($deviceId, $serviceId);
        }
    }

    /*
     * 开锁 -2.7.1 创建设备命令
     *
     *  @param string $commandId   设备命令 ID
     *  @param string $deviceId    下发命令的设备 ID，用于唯一标识一个设备
     *  @param string $command     下发命令的信息
     *  @param string    $serviceId     命令对应的服务 ID，用于标识一个服务。要与 profile 中定义的 serviceId保持一致
     *  @param string    $method        命令服务下具体的命令名称，要与profile 中定义的命令名保持一致
     *  @param string    $paras         命令参数的 jsonString，具体格式需要应用和设备约定
     *  @param string $callbackUrl   通知地址
     *  @param string $expireTime    有效时间
     *  @param string $maxRetrnsmit  命令下发最大重传次数
     * */
    public function Unlocking($deviceId='', $serviceId=''){
        $http_type   = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
        $appId       = '7cef48841c56419b9e6bc4cbd3a0e719';
        $url         = 'https://device.api.ct10649.com:8743/iocm/app/cmd/v1.4.0/deviceCommands?appId=' . $appId;
        $callbackUrl = $http_type . $_SERVER['SERVER_NAME'] . '/index.php/api/table/subscriptCallback';
        $Token       = 'b2c69368ed694c9dba0dd5b3ea85447a';
        $paras       = ["value" => "20"];  // 值”20”为开锁。或设置为”10”
        $data        = [
            'appId'       => $appId,
            'accessToken' => $Token,
            'callbackUrl' => $callbackUrl,
            'deviceId'    => $deviceId,
            'command'     => [
                'serviceId' => $serviceId,
                'method'    => 'SET_DEVICE_LEVEL', // 开锁
                'paras'     => $paras,
            ],
        ];
        //  application/json
        $type    = 3;
        $result  = $this -> curlPost($url, $data, $type);

        trace('-----------------------开锁信息', 'error');
        trace($result, 'error');

        $result  = json_decode($result, true);

        trace($result, 'error');

        return json(['code'=>1, 'msg'=>'ok', 'data'=>$result]);
    }


    /*
    * curl  请求post
    *
    * $type 请求接口类型
    * */
    public function curlPost($url, $data, $type){
        // 初始化
        $curl = curl_init();
        // 设置抓取的url
        curl_setopt($curl, CURLOPT_URL, $url);

        // 登录鉴权接口：x-www-form-urlencoded时的数据则要变为 key=value&key=value的格式 ，类型是 string
        if($type == 1){
            $data = http_build_query($data);
        }

        // 重新获取 accesstoken：application/json
        if($type == 2){
            $headers = ['Content-Type: application/json'];
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
            $data    = json_encode($data);
        }

        // 共用参数---注册设备接口|订阅接口|开锁接口 ：application/json
        if($type == 3){
            $headers = [
                'app_key:' . $data['appId'],
                'Authorization:' . $data['accessToken'],
                'Content-Type: application/json',
            ];
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
            $data    = json_encode($data);
        }

        // 设置头文件的信息作为数据流输出
        curl_setopt($curl, CURLOPT_HEADER, 0);
        // 设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        // 设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 1);
        // post提交的数据
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        // 执行命令
        $result = curl_exec($curl);
        // 关闭URL请求
        curl_close($curl);

        // 显示获得的数据
        return $result;
    }


}