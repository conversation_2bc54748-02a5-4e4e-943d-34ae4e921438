/*!
 * Bootstrap v2.2.1
 *
 * Copyright 2012 Twitter, Inc
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Designed and built with all the love in the world @twitter by @mdo and @fat.
 */

// CSS Reset
@import "_reset.less";

// Core variables and mixins
@import "_variables.less"; // Modify this for custom colors, font-sizes, etc
@import "bs/less/mixins.less";

// Grid system and page structure
@import "bs/less/scaffolding.less";
@import "bs/less/grid.less";
@import "bs/less/layouts.less";

// Base CSS
@import "bs/less/type.less";
@import "bs/less/code.less";
@import "bs/less/forms.less";
@import "bs/less/tables.less";

// Components: common
@import "bs/less/sprites.less";
@import "bs/less/dropdowns.less";
@import "bs/less/wells.less";
@import "bs/less/component-animations.less";
@import "bs/less/close.less";

// Components: Buttons & Alerts
@import "bs/less/buttons.less";
@import "bs/less/button-groups.less";
@import "bs/less/alerts.less"; // Note: alerts share common CSS with buttons and thus have styles in buttons.less

// Components: Nav
@import "bs/less/navs.less";
@import "bs/less/navbar.less";
@import "bs/less/breadcrumbs.less";
@import "bs/less/pagination.less";
@import "bs/less/pager.less";

// Components: Popovers
@import "bs/less/modals.less";
@import "bs/less/tooltip.less";
@import "bs/less/popovers.less";

// Components: Misc
//@import "bs/less/thumbnails.less";
@import "bs/less/media.less";
@import "bs/less/labels-badges.less";
@import "bs/less/progress-bars.less";
@import "bs/less/accordion.less";
@import "bs/less/carousel.less";
@import "bs/less/hero-unit.less";

// Utility classes
@import "bs/less/utilities.less"; // Has to be last to override when necessary

