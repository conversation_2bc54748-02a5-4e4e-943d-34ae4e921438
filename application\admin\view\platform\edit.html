<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-field="nickname" data-source="user/user" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text" value="{$row.name}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Contacts')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-contacts" class="form-control" name="row[contacts]" type="text" value="{$row.contacts}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mobile" class="form-control" name="row[mobile]" type="text" value="{$row.mobile}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Notes')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-notes" class="form-control " rows="5" name="row[notes]" cols="50">{$row.notes}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Join_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-join_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[join_time]" type="text" value="{:$row.join_time?datetime($row.join_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Fcbl')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-fcbl" class="form-control" step="0.01" name="row[fcbl]" type="number" value="{$row.fcbl}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">免单次数:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-exemption_count" class="form-control" step="0.01" data-rule="required" name="row[exemption_count]" type="number" value="{$row.exemption_count}">
        </div>
    </div>
    
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Expire_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-expire_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[expire_time]" type="text" value="{:$row.expire_time?datetime($row.expire_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>
        </div>
    </div>
    
    <div class="form-group">
        <label for="username" class="control-label col-xs-12 col-sm-2">管理员账号:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="username" name="admin[username]" value="{$admin.username}" data-rule="required;username" />
        </div>
    </div>
    <div class="form-group">
        <label for="username" class="control-label col-xs-12 col-sm-2">管理员密码:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="password" class="form-control" id="password" name="admin[password]" value="" />
        </div>
    </div>
    <div class="form-group">
        <label for="email" class="control-label col-xs-12 col-sm-2">Email:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="email" class="form-control" id="email" name="admin[email]" value="{$admin.email}" data-rule="required;email" />
        </div>
    </div>
    
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
