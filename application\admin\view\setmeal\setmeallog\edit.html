<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Setmeal_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-setmeal_id" data-rule="required" data-source="setmeal/index" class="form-control selectpage" name="row[setmeal_id]" type="text" value="{$row.setmeal_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_id" data-rule="required" data-source="agent/index" class="form-control selectpage" name="row[agent_id]" type="text" value="{$row.agent_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hospital_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hospital_id" data-rule="required" data-source="hospital/index" class="form-control selectpage" name="row[hospital_id]" type="text" value="{$row.hospital_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Set_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-set_name" class="form-control" name="row[set_name]" type="text" value="{$row.set_name}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Effective_start')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-effective_start" class="form-control" name="row[effective_start]" type="number" value="{$row.effective_start}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Effective_end')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-effective_end" class="form-control" name="row[effective_end]" type="number" value="{$row.effective_end}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
