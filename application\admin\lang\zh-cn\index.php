<?php

return [
    'Title'                                                      => '标题',
    'Search menu'                                                => '搜索菜单',
    'Layout Options'                                             => '布局设定',
    'Fixed Layout'                                               => '固定布局',
    'You can\'t use fixed and boxed layouts together'            => '盒子模型和固定布局不能同时启作用',
    'Boxed Layout'                                               => '盒子布局',
    'Activate the boxed layout'                                  => '盒子布局最大宽度将被限定为1250px',
    'Toggle Sidebar'                                             => '切换菜单栏',
    'Toggle the left sidebar\'s state (open or collapse)'        => '切换菜单栏的展示或收起',
    'Sidebar Expand on Hover'                                    => '菜单栏自动展开',
    'Let the sidebar mini expand on hover'                       => '鼠标移到菜单栏自动展开',
    'Toggle Right Sidebar Slide'                                 => '切换右侧操作栏',
    'Toggle between slide over content and push content effects' => '切换右侧操作栏覆盖或独占',
    'Toggle Right Sidebar Skin'                                  => '切换右侧操作栏背景',
    'Toggle between dark and light skins for the right sidebar'  => '将右侧操作栏背景亮色或深色切换',
    'Show sub menu'                                              => '显示菜单栏子菜单',
    'Always show sub menu'                                       => '菜单栏子菜单将始终显示',
    'Disable top menu badge'                                     => '禁用顶部彩色小角标',
    'Disable top menu badge without left menu'                   => '左边菜单栏的彩色小角标不受影响',
    'Skins'                                                      => '皮肤',
    'You\'ve logged in, do not login again'                      => '你已经登录，无需重复登录',
    'Username or password can not be empty'                      => '用户名密码不能为空',
    'Username or password is incorrect'                          => '用户名或密码不正确',
    'Username is incorrect'                                      => '用户名不正确',
    'Password is incorrect'                                      => '密码不正确',
    'Admin is forbidden'                                         => '管理员已经被禁止登录',
    'Please try again after 1 day'                               => '请于1天后再尝试登录',
    'Login successful'                                           => '登录成功!',
    'Logout successful'                                          => '退出成功!',
    'Verification code is incorrect'                             => '验证码不正确',
    'Wipe cache completed'                                       => '清除缓存成功',
    'Wipe cache failed'                                          => '清除缓存失败',
    'Wipe cache'                                                 => '清空缓存',
    'Wipe all cache'                                             => '一键清除缓存',
    'Wipe content cache'                                         => '清空内容缓存',
    'Wipe template cache'                                        => '清除模板缓存',
    'Wipe addons cache'                                          => '清除插件缓存',
    'Check for updates'                                          => '检测更新',
    'Discover new version'                                       => '发现新版本',
    'Go to download'                                             => '去下载更新',
    'Currently is the latest version'                            => '当前已经是最新版本',
    'Ignore this version'                                        => '忽略此次更新',
    'Do not remind again'                                        => '不再提示',
    'Your current version'                                       => '你的版本是',
    'New version'                                                => '新版本',
    'Release notes'                                              => '更新说明',
    'Latest news'                                                => '最新消息',
    'View more'                                                  => '查看更多',
    'Links'                                                      => '相关链接',
    'Docs'                                                       => '官方文档',
    'Forum'                                                      => '交流社区',
    'QQ qun'                                                     => 'QQ交流群',
    'Captcha'                                                    => '验证码',
];
