<?php


namespace app\api\controller;


use app\common\controller\Api;
use cabinet\Cabinet as cab;

class Dayu extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    function index()
    {
        echo "hello moto";
        $url = 'https://device.api.ct10649.com:8743/' . 'iocm/app/sec/v1.1.0/login';
        $data = [
            'appId' => '7cef48841c56419b9e6bc4cbd3a0e719',
            'secret' => 'bc0b94421c9241b0bcc294b6ceacbf2c',
        ];
        $type = 1;
        $result = $this->curlPost($url, $data, $type);
        $result = json_decode($result, true);
        print_r($result);
    }
    
    /*
     * curl  请求post     *
     * $type 请求接口类型
     * */

    public function curlPost($url, $data, $type) {
        // 初始化
        $curl = curl_init();
        // 设置抓取的url
        curl_setopt($curl, CURLOPT_URL, $url);

        // 登录鉴权接口：x-www-form-urlencoded时的数据则要变为 key=value&key=value的格式 ，类型是 string
        if ($type == 1) {
            $data = http_build_query($data);
        }

        // 重新获取 accesstoken：application/json
        if ($type == 2) {
            $headers = ['Content-Type: application/json'];
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
            $data = json_encode($data);
        }

        // 共用参数---注册设备接口|订阅接口|开锁接口 ：application/json
        if ($type == 3) {
            $headers = [
                'app_key:' . $data['appId'],
                'Authorization:' . $data['accessToken'],
                'Content-Type: application/json',
            ];
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
            $data = json_encode($data);
        }

        // 设置头文件的信息作为数据流输出
        curl_setopt($curl, CURLOPT_HEADER, 0);
        // 设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        // 设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 1);
        // post提交的数据
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        // 执行命令
        $result = curl_exec($curl);
        // 关闭URL请求
        curl_close($curl);

        // 显示获得的数据
        return $result;
    }
    
}