/*!
 * Bootstrap-select v1.11.2 (http://silviomoreto.github.io/bootstrap-select)
 *
 * Copyright 2013-2016 bootstrap-select
 * Licensed under MIT (https://github.com/silviomoreto/bootstrap-select/blob/master/LICENSE)
 */
!function(a,b){"function"==typeof define&&define.amd?define(["jquery"],function(a){return b(a)}):"object"==typeof exports?module.exports=b(require("jquery")):b(jQuery)}(this,function(a){/*!
 * Translated default messages for bootstrap-select.
 * Locale: AR (Arabic)
 * Author: Yasser Lotfy <<EMAIL>>
 */
!function(a){a.fn.selectpicker.defaults={noneSelectedText:"لم يتم إختيار شئ",noneResultsText:"لا توجد نتائج مطابقة لـ {0}",countSelectedText:function(a,b){return 1==a?"{0} خيار تم إختياره":"{0} خيارات تمت إختيارها"},maxOptionsText:function(a,b){return[1==a?"تخطى الحد المسموح ({n} خيار بحد أقصى)":"تخطى الحد المسموح ({n} خيارات بحد أقصى)",1==b?"تخطى الحد المسموح للمجموعة ({n} خيار بحد أقصى)":"تخطى الحد المسموح للمجموعة ({n} خيارات بحد أقصى)"]},selectAllText:"إختيار الجميع",deselectAllText:"إلغاء إختيار الجميع",multipleSeparator:"، "}}(a)});