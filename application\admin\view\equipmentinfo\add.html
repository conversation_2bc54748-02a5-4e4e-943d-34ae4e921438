<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

<!--    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Platform_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-platform_id" data-rule="required" data-source="platform/index" class="form-control selectpage" name="row[platform_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_id" data-rule="required" data-source="agent/index" class="form-control selectpage" name="row[agent_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hospital_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hospital_id" data-rule="required" data-source="hospital/index" class="form-control selectpage" name="row[hospital_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Departments_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-departments_id" data-rule="required" data-source="departments/index" class="form-control selectpage" name="row[departments_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Equipment_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-equipment_id" data-rule="required" data-source="equipment/index" class="form-control selectpage" name="row[equipment_id]" type="text" value="">
        </div>
    </div>-->
    <input id="c-equipment_id" name="row[equipment_id]" type="hidden" value="{$equipment_id}" />
<!--    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-code" data-rule="required" class="form-control" name="row[code]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Devicename')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-devicename" data-rule="required" class="form-control" name="row[devicename]" type="text">
        </div>
    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Deviceno')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-deviceno" class="form-control" name="row[deviceno]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Lyname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-lyname" class="form-control" name="row[lyname]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mac')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mac" class="form-control" name="row[mac]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Servicecode')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-servicecode" class="form-control" name="row[servicecode]" type="text" value="0000FFE0-0000-1000-8000-00805F9B34FB">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Charactercode')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-charactercode" class="form-control" name="row[charactercode]" type="text" value="0000FFE1-0000-1000-8000-00805F9B34FB">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nb_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nb_number" class="form-control" name="row[nb_number]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="1"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Notes')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-notes" class="form-control " rows="5" name="row[notes]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
