<?php

namespace app\admin\model;

use think\Model;


class Order extends Model
{

    

    //数据库
    protected $connection = 'database';
    // 表名
    protected $name = 'order';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text',
        'pay_types_text',
        'pay_status_text',
        'pay_time_text',
        'returntime_text',
        'is_branch_text'
    ];
    

    
    public function getStatusList()
    {
        return ['1' => __('Status 1'), '2' => __('Status 2'), '3' => __('Status 3')];
    }

    public function getPayTypesList()
    {
        return ['0' => __('Pay_types 0'), '1' => __('Pay_types 1')];
    }

    public function getPayStatusList()
    {
        return ['0' => __('Pay_status 0'), '1' => __('Pay_status 1'), '2' => __('Pay_status 2'), '3' => __('Pay_status 3'), '4' => __('Pay_status 4'), '5' => __('Pay_status 5')];
    }

    public function getIsBranchList()
    {
        return ['1' => __('Is_branch 1'), '2' => __('Is_branch 2')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getPayTypesTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['pay_types']) ? $data['pay_types'] : '');
        $list = $this->getPayTypesList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getPayStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['pay_status']) ? $data['pay_status'] : '');
        $list = $this->getPayStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getPayTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['pay_time']) ? $data['pay_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getReturntimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['returntime']) ? $data['returntime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getIsBranchTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['is_branch']) ? $data['is_branch'] : '');
        $list = $this->getIsBranchList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    protected function setPayTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setReturntimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function platform()
    {
        return $this->belongsTo('Platform', 'platform_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function agent()
    {
        return $this->belongsTo('Agent', 'agent_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    
    public function hospital()
    {
        return $this->belongsTo('Hospital', 'hospital_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
