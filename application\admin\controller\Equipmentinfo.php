<?php

namespace app\admin\controller;

use app\common\controller\Backend;

use app\admin\library\Auth;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;
use Endroid\QrCode\QrCode;
use MyImages;
/**
 * 设备信息
 *
 * @icon fa fa-circle-o
 */
class Equipmentinfo extends Backend
{
    protected $noNeedLogin = ['getewm'];
    /**
     * Equipmentinfo模型对象
     * @var \app\admin\model\Equipmentinfo
     */
    protected $model = null;
    protected $equipment_id = 0;
    
    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Equipmentinfo;
        $this->view->assign("statusList", $this->model->getStatusList());
        
        $equipment_id = input('equipment_id');
        $this->equipment_id = $equipment_id;
        $this->assign('equipment_id',$equipment_id);
        
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax())
        {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField'))
            {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                    ->with(['admin','agent','hospital','hospitaldepartments','equipment'])
                    ->where($where)
                    ->where(['equipment_id'=>  $this->equipment_id])
                    ->order($sort, $order)
                    ->count();

            $list = $this->model
                    ->with(['admin','agent','hospital','hospitaldepartments','equipment'])
                    ->where($where)
                    ->where(['equipment_id'=>  $this->equipment_id])
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();

            foreach ($list as $row) {
                $row->visible(['id','platform_id','agent_id','hospital_id','departments_id','equipment_id','code','devicename','deviceno','lyname','mac','servicecode','charactercode','status','notes','createtime','updatetime','voltage','voltagetime','nb_number']);
                $row->visible(['admin']);
				$row->getRelation('admin')->visible(['nickname']);
				$row->visible(['agent']);
				$row->getRelation('agent')->visible(['name']);
				$row->visible(['hospital']);
				$row->getRelation('hospital')->visible(['name']);
				$row->visible(['hospitaldepartments']);
				$row->getRelation('hospitaldepartments')->visible(['deptname']);
				$row->visible(['equipment']);
				$row->getRelation('equipment')->visible(['mainname']);
            }
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }
    
    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                
//                if(db('equipment_info')->where(['code'=>$params['code']])->find()){
//                    $this->error('设备编码重复');
//                    exit();
//                }
                
                $equipment = db('equipment')->where(['id'=>$params['equipment_id']])->find();
                
                $params['platform_id'] = $equipment['platform_id'];
                $params['agent_id'] = $equipment['agent_id'];
                $params['hospital_id'] = $equipment['hospitals_id']; 
                $params['departments_id'] = $equipment['departments_id']; 
                $params['kpl'] = $this->rand_str();
                
                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }
    
    
     /**
      * 方法二：获取随机字符串
      * @param int $randLength 长度
      * @param int $addtime 是否加入当前时间戳
      * @param int $includenumber 是否包含数字
      * @return string
      */
     public function rand_str($randLength = 32, $addtime = 1, $includenumber = 1)
     {
         if ($includenumber) {
             $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHJKLMNPQEST123456789';
         } else {
             $chars = 'abcdefghijklmnopqrstuvwxyz';
         }
         $len = strlen($chars);
         $randStr = '';
         for ($i = 0; $i < $randLength; $i++) {
             $randStr .= $chars[mt_rand(0, $len - 1)];
         }
         $tokenvalue = $randStr;
         if ($addtime) {
             $tokenvalue = $randStr . time();
         }
         
         $res = db('equipment_info')->where(['kpl'=>$tokenvalue])->find();
         if($res){
             $tokenvalue = $this->rand_str();
         }
         
         return $tokenvalue;
     }
     
     
     public function ewm(){
         $equipment_info_id = input('equipment_info_id');
         $this->assign('equipment_info_id',$equipment_info_id);
         return $this->view->fetch();
     }
     public function getewm() {
         
        $equipment_info_id = input('equipment_info_id');
        $equipment_info = db('equipment_info')->where(['id'=>$equipment_info_id])->find();
        if($equipment_info['ewm'] == ''){
            $staff_url = 'https://'.$_SERVER['HTTP_HOST'].'/kpl/'.$equipment_info['kpl'];
//            $staff_url = 'https://'.'gxc.quanjing-vr.com'.'/kpl/'.$equipment_info['kpl'];
            $qrCode = new QrCode();
            $qrCode->setText($staff_url)->setSize(512);
            $qr_name = uniqid() . '.png';
            $qr_path = ROOT_PATH . 'public/uploads/ewm/' . $qr_name;
            $qrCode->save($qr_path);
            $ewmUrl = 'uploads/ewm/' . $qr_name;
            db('equipment_info')->where(['id'=>$equipment_info_id])->update(array('ewm'=>$ewmUrl));
        }else{
            $ewmUrl = $equipment_info['ewm'];
        }
        
        $img = new \MyImages\Generate();
        $notes = db('equipment')->where(['id' => $equipment_info['equipment_id']])->value('notes');
        $res = $img->createSharePng($ewmUrl,$equipment_info['deviceno'],$notes);
        
        print_r($res);die();
    }
}
