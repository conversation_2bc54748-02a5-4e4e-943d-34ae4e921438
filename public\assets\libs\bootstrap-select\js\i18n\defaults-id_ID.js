/*
 * Translated default messages for bootstrap-select.
 * Locale: ID (Indonesian; Bahasa Indonesia)
 * Region: ID (Indonesia)
 */
(function ($) {
  $.fn.selectpicker.defaults = {
    noneSelectedText: 'Tidak ada yang dipilih',
    noneResultsText: 'Tidak ada yang cocok {0}',
    countSelectedText: '{0} terpilih',
    maxOptionsText: ['Mencapai batas (maksimum {n})', 'Mencapai batas grup (maksimum {n})'],
    selectAllText: '<PERSON><PERSON><PERSON>mu<PERSON>',
    deselectAllText: 'Ha<PERSON> Semua',
    multipleSeparator: ', '
  };
})(jQuery);
