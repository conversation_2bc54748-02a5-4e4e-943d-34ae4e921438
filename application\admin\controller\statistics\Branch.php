<?php

namespace app\admin\controller\statistics;

use app\common\controller\Backend;

/**
 * 订单返佣明细
 *
 * @icon fa fa-circle-o
 */
class Branch extends Backend
{
    
    /**
     * Branch模型对象
     * @var \app\admin\model\Branch
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Branch;
        $this->view->assign("typesList", $this->model->getTypesList());
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax())
        {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField'))
            {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            
            $adminInfo = $this->adminInfo;
            list($branchWhere) = $this->branchBuildparams();
            
            
            
            $total = $this->model
                    ->with(['order'])
                    ->where($where)
                    ->where($branchWhere)
                    ->order($sort, $order)
                    ->count();

            $list = $this->model
                    ->with(['order'])
                    ->where($where)
                    ->where($branchWhere)
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();

            foreach ($list as $row) {
                switch ($row['types']) {
                    case 1:
                        $surface = 'platform';
                        break;
                    case 2:
                        $surface = 'agent';
                        break;
                    case 3:
                        $surface = 'hospital';
                        break;
                    default:
                        break;
                }
                $row['member_name'] = db($surface)->where(['id'=>$row['member_id']])->value('name');
                
                
                if($row['change_types'] == 1){
                    $row['service_charge'] = sprintf("%.2f",$row['order_money'] * $row['service_charge']);
                    $row['fcbl'] = $row['fcbl'] / 100;
                }else{
                    $row['service_charge'] = '';
                }
                
                $row->visible(['id','platform_id','agent_id','hospital_id','departments_id','equipment_id','equipment_info_id','order_id','types','member_id','member_name','order_money','fcbl','money','createtime','order','service_charge','change_types']);
                $row->getRelation('order')->visible(['info','sn','user_id','money']);
                
            }
            
            
            
            
            
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list,'a'=>$adminInfo);

            return json($result);
        }
        return $this->view->fetch();
    }
}
