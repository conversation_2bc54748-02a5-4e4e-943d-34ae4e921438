<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\admin\library\Auth;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;
use fast\Random;
use weixin;

/**
 * 提现记录管理
 *
 * @icon fa fa-circle-o
 */
class Carrylog extends Backend {

    /**
     * Carrylog模型对象
     * @var \app\admin\model\Carrylog
     */
    protected $model = null;

    public function _initialize() {
        parent::_initialize();
        $this->model = new \app\admin\model\Carrylog;
        $this->view->assign("typesList", $this->model->getTypesList());
        $this->view->assign("txStatesList", $this->model->getTxStatesList());
        $this->view->assign("statesList", $this->model->getStatesList());
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index() {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            list($branchWhere) = $this->branchBuildparams();
            $total = $this->model
                    ->with(['admin'])
                    ->where($where)
                    ->where($branchWhere)
                    ->order($sort, $order)
                    ->count();

            $list = $this->model
                    ->with(['admin'])
                    ->where($where)
                    ->where($branchWhere)
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();

            foreach ($list as $row) {

                switch ($row['types']) {
                    case 2:
                        $surface = 'platform';
                        break;
                    case 3:
                        $surface = 'agent';
                        break;
                    case 4:
                        $surface = 'hospital';
                        break;
                    default:
                        break;
                }
                $row['member_name'] = db($surface)->where(['id' => $row['member_id']])->value('name');

                $row->visible(['id', 'types', 'member_id', 'member_name', 'money', 'tx_states', 'bank_khh', 'bank_name', 'bank_card', 'info', 'states', 'createtime', 'updatetime', 'admin_id', 'admin_info']);
                $row->visible(['admin']);
                $row->getRelation('admin')->visible(['nickname']);
            }
            $list = collection($list)->toArray();

            $admin = $this->adminInfo;
            $surface = '';
            switch ($admin['types']) {
                case 2:
                    $surface = 'platform';
                    $where = array('id' => $admin['platform_id']);
                    break;
                case 3:
                    $surface = 'agent';
                    $where = array('id' => $admin['details_id']);
                    break;
                case 4:
                    $surface = 'hospital';
                    $where = array('id' => $admin['details_id']);
                    break;
                default:
                    break;
            }
            if ($surface != '') {
                $balance = db($surface)->where($where)->value('balance');
            } else {
                $balance = 0;
            }


            $result = array("total" => $total, "rows" => $list, 'balance' => $balance, 'where' => $where);

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add() {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $d = date('d');
                if($d <= 3 || $d >= 5){
//                    $this->error('只能每月三号到五号可以提现');
                }
//                $this->error($this->adminInfo);exit();
                if ($this->adminInfo['types'] == 1) {
                    $this->error('无体现权限');
                    exit();
                }

                if ($params['money'] <= 0) {
                    $this->error('提现金额不得小于0');
                    exit();
                }
//                var_dump($this->adminInfo);die;

                if ($params['money'] > $this->adminInfo['detils']['balance']) {
                    $this->error('余额不足');
                    exit();
                }

                $params['types'] = $this->adminInfo['types'];
                $params['member_id'] = $this->adminInfo['detils']['id'];
                $params['states'] = 1;
                $params['createtime'] = time();
                $params['updatetime'] = '';

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {

                    switch ($this->adminInfo['types']) {
                        case 2:
                            $surface = 'platform';
                            break;
                        case 3:
                            $surface = 'agent';
                            break;
                        case 4:
                            $surface = 'hospital';
                            break;
                        default:
                            break;
                    }

                    db($surface)->where(['id' => $this->adminInfo['detils']['id']])->setDec('balance', $params['money']);

                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null) {
        $row = $this->model->get($ids);
        $row_states = $row['states'];
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            $params['admin_id'] = $this->adminInfo['id'];
            if($row['states'] == 2 || $row['states'] == 4){
                if($params['states'] == 2){
                    $this->error('打款中或已打款请勿重复操作');
                }
            }
            if($params['states'] == 2){
                $params['states'] = 4;
            }
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    if($params['states'] == 4){
                        $return = $this->paymentToWeChat($ids);
                        if($return['success']){
                            //企业付款到零钱成功
                            db('carrylog')->where(['id'=>$ids])->update([
                                'states' => 2,
                                'partner_trade_no' => $return['data']['partner_trade_no'],
                                'payment' => json_encode($return['data']['info']),
                                'paytime' => time(),
                            ]);
                            $this->success();
                        }else{
                            db('carrylog')->where(['id'=>$ids])->update([
                                'states' => $row_states,
                            ]);
                            $this->error($return['msg']);
                        }
                    }
                    
                    if($params['states'] == 3){
                        $carrylog = db('carrylog')->where(['id' => $ids])->find();
                        switch ($carrylog['types']) {
                            case 2:
                                $surface = 'platform';
                                break;
                            case 3:
                                $surface = 'agent';
                                break;
                            case 4:
                                $surface = 'hospital';
                                break;
                            default:
                                break;
                        }
                        if($surface != ''){
                            db($surface)->where(['id' => $carrylog['member_id']])->setInc('balance', $carrylog['money']);
                        }
                    }
                    
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    

    //企业付款到微信
    public function paymentToWeChat($id) {
        $return = array(
            'success' => false,
            'msg' => '',
        );
        
        $row = db('carrylog')->where(['id' => $id])->find();
        if($row){
            if($row['states'] == 4){
                //获得绑定的提现人会员id
                $member = '';
                switch ($row['types']) {
                    case 2:
                        $member = db('platform')->where(['id' => $row['member_id']])->field('id,name,user_id')->find();
                        break;
                    case 3:
                        $member = db('agent')->where(['id' => $row['member_id']])->field('id,name,user_id')->find();
                        break;
                    case 4:
                        $member = db('hospital')->where(['id' => $row['member_id']])->field('id,name,user_id')->find();
                        break;
                    default:
                        break;
                }
                if($member){
                    $user_id = $member['user_id'];
                    $name = $member['name'];
                    if($user_id != ''){
                        $user = db('user')->where(['id' => $user_id])->find();
                        if($user){
                            $config = [
                                'appid' => $this->wx_config['appid'],
                                'mch_id' => $this->wx_config['mch_id'],
                                'key' => $this->wx_config['wxkey'],
                                'sslcert_path' => $this->wx_config['apiclient_cert'], //企业付款时必须上传证书
                                'sslkey_path' => $this->wx_config['apiclient_key']
                            ];
                            
                            $param = [
                                'partner_trade_no' => $this->getOrdersn(), //商户订单号，需保持唯一性(只能是字母或者数字，不能包含有其他字符)
                                'openid' => $user['openid'], //商户appid下，某用户的openid
                                'amount' => $row['money'] * 100, //企业付款金额，单位为分
                                'desc' => $name . '，收益提现,提现金额：' . $row['money'], //企业付款备注，必填。注意：备注中的敏感词会被转成字符*
                                're_user_name' => '', //收款用户真实姓名，NO_CHECK 时可不传，如果check_name设置为FORCE_CHECK，则必填用户真实姓名
                            ];

                            $wechatMchPay = new weixin\WechatMchPay($config);
                            $info = $wechatMchPay->payChange($param);
                            if($info['return_code'] == 'SUCCESS'){
                                //接口调用成功
                                if($info['result_code'] == 'SUCCESS'){
                                    //付款成功 
                                    $return['success'] = true;
                                    $return['msg'] = '付款成功';
                                    $return['data'] = [
                                        'partner_trade_no' => $param['partner_trade_no'],
                                        'info' => $info,
                                    ];
                                    
                                }else{
                                    //付款失败
                                    $return['msg'] = $info['err_code_des'];
                                }
                            }else{
                                //接口调用失败
                                $return['msg'] = '调用接口失败';
                            }
                        }else{
                            $return['msg'] = '微信账号不存在';
                        }
                    }else{
                        $return['msg'] = '提现用户为绑定提现微信账号';
                    }
                }else{
                    $return['msg'] = '提现用户不存在';
                }
            }else{
                $return['msg'] = '状态错误';
            }
        }else{
            $return['msg'] = '提现记录不存在';
        }
        return $return;
    }
    
    //随机生成订单编号
    private function getOrdersn() {
        $no = 'ord' . date('YmdHis') . rand(10000000, 99999999);
        if (db('carrylog')->where('partner_trade_no', $no)->find()) {
            $no = $this->getOrdersn();
        } else {
            return $no;
        }
    }

}
