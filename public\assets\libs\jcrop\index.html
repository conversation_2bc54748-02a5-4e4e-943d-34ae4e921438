<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Jcrop v2.0.0-RC1 | Jcrop Jcrop</title>
    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.10.2/jquery.min.js"></script>
    <link rel="stylesheet" href="demos/demo_files/main.css">
    <link rel="stylesheet" href="demos/demo_files/demos.css">
  </head>
  <body>
    <div class="navbar navbar-fixed-top">
      <div class="navbar-inner">
        <div class="container">
          <button type="button" data-toggle="collapse" data-target="nav-collapse" class="btn btn-navbar"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></button><a class="brand">Jcrop</a>
          <div class="nav-collapse collapse">
            <ul class="nav">
              <li><a href="demos/">Demos</a>
              </li>
              <li><a href="http://beta.jcrop.org/doc/">Docs</a>
              </li>
              <li><a href="http://beta.jcrop.org/contact/">Contact</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="container">
      <div class="row">
        <div class="span12">
          <div class="jc-demo-box">
            <div class="page-header">
              <ul class="breadcrumb first">
                <li><a href="http://jcrop.org/">Jcrop.org</a><span class="divider">/</span></li>
                <li class="active">Jcrop v2.0.0-RC1</li>
              </ul>
              <h1>Jcrop v2.0.0-RC1</h1>
            </div>
            <div class="main-menu menu-box">
              <h3>Jcrop Demos</h3>
              <ul class="links">
                <li><a href="demos/basic.html">Hello World</a></li>
                <li><a href="demos/thumbnail.html">Thumbnail Preview</a></li>
                <li><a href="demos/panel.html">Feature Panel</a></li>
                <li><a href="demos/coords.html">Dimensions</a></li>
                <li><a href="demos/circle.html">Circles</a></li>
              </ul>
            </div>
            <p><b>Build Date:</b> Sun Sep 08 2013 02:56:09 GMT+0000 (UTC)</p><h3>What&#39;s Included</h3>

<p>The this archive contains the latest version of <strong>Jcrop</strong>,
comprised of the following:</p>

<ul><li>Latest version of <a href="js/Jcrop.js"><strong>Jcrop.js</strong></a> and <a href="css/Jcrop.css"><strong>Jcrop.css</strong></a>, the primary requisite files</li><li>Minimized versions of both of the above (<a href="js/Jcrop.min.js">Jcrop.min.js</a>, <a href="css/Jcrop.min.css">Jcrop.min.css</a>)</li><li>Several demos are also included, see links in menu at right</li></ul>
            <hr><h1>Jcrop Image Cropping Plugin</h1>

<p>Jcrop is the quick and easy way to add image cropping functionality to
your web application. It combines the ease-of-use of a typical jQuery
plugin with a powerful cross-platform DHTML cropping engine that is
faithful to familiar desktop graphics applications.</p>

<h3>Feature Overview</h3>

<ul><li>Attaches unobtrusively to any image or block element</li><li>Completely based on true prototypical Javascript objects for extreme flexibility</li><li>Supports multiple active selections, per-selection customization</li><li>Supports aspect ratio locking, minimum/maximum size, and other features</li><li>Acts as in-line form element, can receive focus, tab through</li><li>Keyboard support for nuding selections and trapping other keys</li><li>Inherently API-driven and stylable with CSS</li><li>Mobile touch support for iOS and Android</li></ul>

<h3>Cross-platform Compatibility</h3>

<p>The current version of Jcrop has been cross-platform tested and core functionality
works in all the following browsers:</p>

<ul><li>Firefox 3+</li><li>Safari 4+</li><li>Opera 9.5+</li><li>Google Chrome 14+</li><li>Internet Explorer 7+</li></ul>

<p>Older versions of some browsers may also work.</p>

<p>Always thoroughly test any desired functionality on all target platforms and devices.</p>

<h5>Legacy IE Compatibility</h5>

<p>Internet Explorer 6 suffers some visual problems with the new CSS structure
and will not be explicitly supported from v2.x and up. Currently Jcrop can still
be used in IE6, it just looks ugly. IE7 and newer versions deliver a nearly flawless
Jcrop experience.</p>

<hr/>

<h2>MIT License</h2>

<p><strong>Jcrop is free software under MIT License.</strong></p>

<h4>Copyright (c) 2008-2015 Tapmodo Interactive LLC</h4>

<h5><a href="http://github.com/tapmodo/Jcrop">http://github.com/tapmodo/Jcrop</a></h5>

<p>Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
&quot;Software&quot;), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:</p>

<p>The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.</p>

<p>THE SOFTWARE IS PROVIDED &quot;AS IS&quot;, WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.</p>
            <div class="tapmodo-footer"><a href="http://tapmodo.com" class="tapmodo-logo segment">tapmodo.com</a>
              <div class="segment"><b>&copy; 2008-2015 Tapmodo Interactive LLC</b>
                <div>Jcrop is free software released under <a href="../MIT-LICENSE.txt">MIT License</a></div>
              </div>
            </div>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>