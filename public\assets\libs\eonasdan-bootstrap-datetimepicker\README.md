# Bootstrap 3 Date/Time Picker
![GitHub version](https://badge.fury.io/gh/Eonasdan%2Fbootstrap-datetimepicker.png)&nbsp;&nbsp;&nbsp;![<PERSON>](https://travis-ci.org/Eonasdan/bootstrap-datetimepicker.svg?branch=development)

![DateTimePicker](http://i.imgur.com/nfnvh5g.png)

## [View the manual and demos](http://eonasdan.github.io/bootstrap-datetimepicker/)

## [Installation instructions](http://eonasdan.github.io/bootstrap-datetimepicker/Installing/)

## [Change Log](http://eonasdan.github.io/bootstrap-datetimepicker/Changelog/)

### This issue tracker is no longer actively monitored.

# Version 5

Version 5 is being completely rewritten in ES6 and modularized as Tempus Dominus.

v5 is [in alpha](https://github.com/tempusdominus/bootstrap-3).


