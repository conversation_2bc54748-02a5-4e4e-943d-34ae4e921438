<div class="panel panel-default panel-intro">
    
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="status">
            <li class="active"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            {foreach name="statusList" item="vo"}
            <li><a href="#t-{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>
            {/foreach}
        </ul>
    </div>


    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
<!--                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('statistics/total/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('statistics/total/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('statistics/total/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-import {:$auth->check('statistics/total/import')?'':'hide'}" title="{:__('Import')}" id="btn-import-file" data-url="ajax/upload" data-mimetype="csv,xls,xlsx" data-multiple="false"><i class="fa fa-upload"></i> {:__('Import')}</a>

                        <div class="dropdown btn-group {:$auth->check('statistics/total/multi')?'':'hide'}">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> {:__('More')}</a>
                            <ul class="dropdown-menu text-left" role="menu">
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=normal"><i class="fa fa-eye"></i> {:__('Set to normal')}</a></li>
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=hidden"><i class="fa fa-eye-slash"></i> {:__('Set to hidden')}</a></li>
                            </ul>
                        </div>-->

                        
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('statistics/total/edit')}" 
                           data-operate-del="{:$auth->check('statistics/total/del')}" 
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>


<script id="customformtpl" type="text/html">
    <!--form表单必须添加form-commsearch这个类-->
    <form action="" class="form-commonsearch">
        <div style="border-radius:2px;margin-bottom:10px;background:#f5f5f5;padding:15px 20px;">
            <h4>搜索</h4>
            <hr>
            <div class="row">
                {if condition="$adminInfo.types != 4"}
                <div class="col-xs-12 col-sm-6 col-md-3">
                    <div class="form-group">
                        <label class="control-label">医院</label>
                        <input type="hidden" class="operate" data-name="id" value="="/>
                        <div>
                        {if condition="$adminInfo.types == 2"}
                        <input id="c-id" data-source="hospital/index" data-params='{"custom[platform_id]":{$adminInfo.detils.id}}' data-primary-key="id" data-field="name" class="form-control selectpage" name="id" type="text" value="" style="display:block;">
                        {elseif condition="$adminInfo.types == 3"/}
                        <input id="c-id" data-source="hospital/index" data-params='{"custom[agent_id]":{$adminInfo.detils.id}}' data-primary-key="id" data-field="name" class="form-control selectpage" name="id" type="text" value="" style="display:block;">
                        {else/}
                        <input id="c-id" data-source="hospital/index" data-primary-key="id" data-field="name" class="form-control selectpage" name="id" type="text" value="" style="display:block;">
                        {/if}
                            
                        </div>
                    </div>
                </div>
                {/if}
                <div class="col-xs-12 col-sm-6 col-md-3">
                    <div class="form-group">
                        <label class="control-label">日期</label>
                        <input type="hidden" class="operate" data-name="createtime" value="RANGE"/>
                        <div>
                        <input type="text" class="form-control datetimerange" name="createtime" value=""/>
                        </div>
                    </div>
                </div>
                
                
                <div class="col-xs-12 col-sm-6 col-md-3">
                    <div class="form-group">
                        <label class="control-label"></label>
                        <div class="row">
                            <div class="col-xs-6">
                                <input type="submit" class="btn btn-success btn-block" value="提交"/>
                            </div>
                            <div class="col-xs-6">
                                <input type="reset" class="btn btn-primary btn-block" value="重置"/>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </form>
</script>