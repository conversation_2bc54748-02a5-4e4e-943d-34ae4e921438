<?php

namespace app\admin\controller;

use app\common\controller\Backend;

use app\admin\library\Auth;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;
use fast\Random;

/**
 * 平台管理
 *
 * @icon fa fa-circle-o
 */
class Platform extends Backend
{
    
    /**
     * Platform模型对象
     * @var \app\admin\model\Platform
     */
    protected $model = null;
    protected $modelAdmin = null;
    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Platform;
        $this->modelAdmin = model('Admin');
        $this->view->assign("statusList", $this->model->getStatusList());
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = false;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax())
        {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField'))
            {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                    
                    ->where($where)
                    ->order($sort, $order)
                    ->count();

            $list = $this->model
                    
                    ->where($where)
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();

            foreach ($list as $row) {
                $row->visible(['id','name','contacts','mobile','notes','join_time','fcbl','expire_time','status','createtime','updatetime','balance']);
                
            }
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }
    
    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                
                if($params['fcbl'] > 100){
                    $this->error('分成比例不得大于100');
                    exit();
                }

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    
                    $params_admin = $this->request->post("admin/a");
                    if ($params_admin)
                    {
                        if(!db('admin')->where(['username' => $params_admin['username']])->find()){
                            $params_admin['nickname'] = $params['name'];
                            $params_admin['salt'] = Random::alnum();
                            $params_admin['password'] = md5(md5('123456') . $params_admin['salt']);
                            $params_admin['avatar'] = '/assets/img/avatar.png'; //设置新管理员默认头像。
                            $params_admin['status'] = 'normal';
                            $params_admin['createtime'] = time();
                            $params_admin['platform_id'] = $this->model->id;
                            $params_admin['types'] = 2;
                            $params_admin['hierarchy'] = 2;
                            $params_admin['details_id'] = 0;

                            $result = $this->modelAdmin->validate('Admin.add')->save($params_admin);
                            if ($result === false)
                            {
                                db('platform')->where(['id' => $this->model->id])->delete();
                                $this->error($this->modelAdmin->getError());
                            }

                            $group = array(
                                'uid' => $this->modelAdmin->id,
                                'group_id' => 2,
                            );
                            db('auth_group_access')->insertGetId($group);
                        }else{
                            $this->error('管理员账号不能重复');
                        }
                        
                    }
                    
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }
    
    
    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                if($params['fcbl'] > 100){
                    $this->error('分成比例不得大于100');
                    exit();
                }
                $res = db('agent')->where(['platform_id'=>$ids,'fcbl'=>['>',$params['fcbl']]])->find();
                if($res){
                    $this->error('下级代理商分成比例有大于该数值的，请先联系下级进行修改','','',3000);
                    exit;
                }
                
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $params_admin = $this->request->post("admin/a");
                    if ($params_admin)
                    {
                        $admin = db('admin')->where(['platform_id'=>$ids])->find();
                        if($admin){
                            $update = array(
                                'email' => $params_admin['email'],
                                'updatetime' => time(),
                            );
                            
                            if($admin['username'] != $params_admin['username']){
                                if(!db('admin')->where(['username' => $params_admin['username'],'platform_id'=>['<>',$ids]])->find()){
                                    $this->error('管理员账号重复');
                                }else{
                                    $update['username'] = $params_admin['username'];
                                }
                            }
                            
                            if($params_admin['password'] != ''){
                                $update['password'] = md5(md5($params_admin['password']) . $admin['salt']);
                            }
                            
                            $result = db('admin')->where(['platform_id'=>$ids])->update($update);
                            
                            if (!$result){
                                $this->error('账号更新失败');
                            }
                        }
                    }
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        $admin = db('admin')->where(['platform_id' => $row['id']])->find();
        $this->view->assign("admin", $admin);
        return $this->view->fetch();
    }
}
