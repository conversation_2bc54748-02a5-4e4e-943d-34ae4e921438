<?php

namespace cabinet;

use think\Db;
use think\Config;
use think\Session;
use think\Request;

class Cabinet {

    private $appId;
    private $secret;
    private $apiUrl = 'https://device.api.ct10649.com:8743/';
    private $http_type = '';

    public function __construct($appId, $secret) {
        $this->appId = $appId;
        $this->secret = $secret;
        $this->http_type = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
    }

    public function getToken() {
        $url = $this->apiUrl . 'iocm/app/sec/v1.1.0/login';
        $data = [
            'appId' => $this->appId,
            'secret' => $this->secret,
        ];
        $type = 1;
        $result = $this->curlPost($url, $data, $type);
        $result = json_decode($result, true);
        if (!array_key_exists('error_code', $result)) {
            return ['code' => 1, 'msg' => 'ok', 'data' => $result];
        } else {
            return ['code' => 0, 'msg' => '获取token失败,错误码：' . $result['error_code']];
        }
    }

    /**
     * 设置回调地址
     * @param string $callback_url 回调url
     */
    public function subscript($callback_url, $token) {
        $appId = $this->appId;
        $url = $this->apiUrl . 'iocm/app/sub/v1.2.0/subscriptions';
        $notifyType = 'deviceDataChanged';
        $callbackUrl = $this->http_type . $_SERVER['SERVER_NAME'] . $callback_url;
        $ownerFlag = false;
        $Token = $token;
        $data = [
            'appId' => $appId,
            'notifyType' => $notifyType,
            'callbackUrl' => $callbackUrl,
            'ownerFlag' => $ownerFlag,
            'accessToken' => $Token,
        ];
        //  application/json
        $type = 3;
        $result = $this->curlPost($url, $data, $type);
        $result = json_decode($result, true);
        return ['code' => 1, 'msg' => 'ok', 'data' => $result];
    }

    /**
     * 开锁 -2.7.1 创建设备命令
     * @param string $deviceId deviceId
     * @param string $serviceId serviceId
     * 
     */
    public function Unlocking($deviceId, $serviceId, $callbackUrl, $Token, $position) {
        $appId = $this->appId;
        $url = $this->apiUrl . 'iocm/app/cmd/v1.4.0/deviceCommands?appId=' . $appId;
        $callbackUrl = $this->http_type . $_SERVER['SERVER_NAME'] . $callbackUrl;
        $paras = ["value" => $position];  // 值”20”为开锁。或设置为”10”
        $data = [
            'appId' => $appId,
            'accessToken' => $Token,
            'callbackUrl' => $callbackUrl,
            'deviceId' => $deviceId,
            'expireTime' => 60,
            'command' => [
                'serviceId' => $serviceId,
                'method' => 'SET_DEVICE_LEVEL', // 开锁
                'paras' => $paras,
                ],
        ];
        //  application/json
        $type = 3;
        $result = $this->curlPost($url, $data, $type);
        return $result;
    }

    /*
     * curl  请求post     *
     * $type 请求接口类型
     * */

    public function curlPost($url, $data, $type, $headers = []) {
        // 初始化
        $curl = curl_init();
        // 设置抓取的url
        curl_setopt($curl, CURLOPT_URL, $url);

        // 登录鉴权接口：x-www-form-urlencoded时的数据则要变为 key=value&key=value的格式 ，类型是 string
        if ($type == 1) {
            $data = http_build_query($data);
        }

        // 重新获取 accesstoken：application/json
        if ($type == 2) {
            $headers = ['Content-Type: application/json'];
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
            $data = json_encode($data);
        }

        // 共用参数---注册设备接口|订阅接口|开锁接口 ：application/json
        if ($type == 3) {
            $headers = [
                'app_key:' . $data['appId'],
                'Authorization:' . $data['accessToken'],
                'Content-Type: application/json',
            ];
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
            unset($data['appId']);
            unset($data['accessToken']);
            $data = json_encode($data);
        }

        // 设置头文件的信息作为数据流输出
        curl_setopt($curl, CURLOPT_HEADER, 0);
        // 设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        // 设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 1);
        // post提交的数据
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        // 执行命令
        $result = curl_exec($curl);
        // 关闭URL请求
        curl_close($curl);

        // 显示获得的数据
        return $result;
    }

}
