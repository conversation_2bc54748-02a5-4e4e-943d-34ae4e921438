<?php

namespace app\admin\controller\statistics;

use app\common\controller\Backend;

/**
 * 医院
 *
 * @icon fa fa-circle-o
 */
class Total extends Backend
{
    
    /**
     * Total模型对象
     * @var \app\admin\model\Total
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Total;
        $this->view->assign("joinTypeList", $this->model->getJoinTypeList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = false;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax())
        {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField'))
            {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            list($mywhere) = $this->mybuildparams();
            
            $total = $this->model
                    //->where($where)
                    ->where($mywhere)
                    ->order($sort, $order)
                    ->count();

            $list = $this->model
                    //->where($where)
                    ->where($mywhere)
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();
            
            $filter = $this->request->get("filter", '');
            $filter = (array)json_decode($filter, true);
            $filter = $filter ? $filter : [];
            
            $where = array();
            if (isset($filter['createtime'])) {
                $v = str_replace(' - ', ',', $filter['createtime']);
                $arr = array_slice(explode(',', $v), 0, 2);
                $order_where ['createtime'] = array('between', [strtotime($arr[0]), strtotime($arr[1])]);
            }
            
            foreach ($list as $row) {
                $order_where = $where;
                $order_where['hospital_id'] = $row['id'];
                $row['order_count'] = db('order')->where($order_where)->count();//总使用次数
                $row['order_status1_count'] = db('order')->where($order_where)->where(['status'=> 1])->count();//使用中
                $row['order_status2_count'] = db('order')->where($order_where)->where(['status'=> 2])->count();//代付款
                $row['order_status3_pay1_count'] = db('order')->where($order_where)->where(['status'=> 3,'pay_status' => 1])->count();//已支付 - 正常支付
                $row['order_status3_pay2_count'] = db('order')->where($order_where)->where(['status'=> 3,'pay_status' => 2])->count();//已支付 - 故障免单
                $row['order_status3_pay3_count'] = db('order')->where($order_where)->where(['status'=> 3,'pay_status' => 3])->count();//已支付 - 短时免单
                $row['order_status3_pay4_count'] = db('order')->where($order_where)->where(['status'=> 3,'pay_status' => 4])->count();//已支付 - 系统免单
//                $row['order_money'] = db('order')->where($order_where)->where(['status'=> 3,'pay_status'=> 1])->sum('money');
                $branch_where = $where;
                $branch_where['hospital_id'] = $row['id'];
                if($this->adminInfo['types'] != 1){
                    $branch_where['member_id'] = $this->adminInfo['detils']['id'];
                }
                
                $branch = db('branch')->where($branch_where)->select();
                $row['totalrevenue'] = 0;//总收益
                $row['servicecharge'] = 0;//手续费
                $row['realincome'] = 0;//实际收益
                foreach($branch as $k => $v){
                    if($branch[$k]['change_types'] == 1){
                        $row['totalrevenue'] += $branch[$k]['money'];
                    }else{
                        $row['servicecharge'] -= $branch[$k]['money'];
                    }
                    $row['realincome'] += $branch[$k]['money'];
                }
                $row['branch1'] = $branch;
                $row->visible([
                    'id',
                    'order_count','order_status1_count','order_status2_count','order_status3_pay1_count','order_status3_pay2_count','order_status3_pay3_count','order_status3_pay4_count','totalrevenue','servicecharge','realincome',
                    'platform_id','agent_id','code','name','addr','join_type','fcbl','price','hourlong','freedt','notes','corpname',
                    'kefu','logo_image','introduce_content','status','createtime','updatetime','route','balance','branch1'
                    ]);
            }
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list,'w'=>$order_where);

            return json($result);
        }
        return $this->view->fetch();
    }
}
