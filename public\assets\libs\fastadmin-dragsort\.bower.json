{"name": "dragsort", "version": "1.0.0", "authors": ["<PERSON>"], "description": "A javascript file that provides the ability to sort lists using drag and drop.", "main": "jquery.dragsort.js", "keywords": ["javascript", "sort", "j<PERSON>y"], "license": "http://dragsort.codeplex.com/license", "homepage": "http://dragsort.codeplex.com/", "ignore": ["**/.*", "bower_components", "examples"], "dependencies": {"jquery": ">=1.6.0 <3.0.0"}, "_release": "1.0.0", "_resolution": {"type": "version", "tag": "v1.0.0", "commit": "98dbf8aa97d203b727b34969dc2734e98b6aba65"}, "_source": "https://github.com/karsonzhang/fastadmin-dragsort.git", "_target": "~1.0.0", "_originalSource": "fastadmin-dragsort"}