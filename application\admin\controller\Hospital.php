<?php

namespace app\admin\controller;

use app\common\controller\Backend;

use app\admin\library\Auth;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;
use fast\Random;

/**
 * 医院
 *
 * @icon fa fa-circle-o
 */
class Hospital extends Backend
{
    
    /**
     * Hospital模型对象
     * @var \app\admin\model\Hospital
     */
    protected $model = null;
    protected $modelAdmin = null;
    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Hospital;
        $this->modelAdmin = model('Admin');
        $this->view->assign("joinTypeList", $this->model->getJoinTypeList());
        $this->view->assign("chargingruleList", $this->model->getChargingRuleList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax())
        {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField'))
            {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            list($mywhere) = $this->mybuildparams();
            $total = $this->model
                    ->with(['admin','agent'])
                    ->where($where)
                    ->where($mywhere)
                    ->order($sort, $order)
                    ->count();

            $list = $this->model
                    ->with(['admin','agent'])
                    ->where($where)
                    ->where($mywhere)
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();

            foreach ($list as $row) {
                $row['agent_id_name'] = db('agent')->where(['id'=>$row['agent_id']])->value('name');
                if(!$row['agent_id_name']){
                    $row['agent_id_name'] = '无';
                }
                $row['platform_id_name'] = db('platform')->where(['id'=>$row['platform_id']])->value('name');
                $row->visible(['id','platform_id','platform_id_name','agent_id','agent_id_name','code','name','addr','join_type','fcbl','price','hourlong','freedt','notes','corpname','kefu','logo_image','introduce_content','status','createtime','updatetime','balance']);
                $row->visible(['admin']);
				$row->getRelation('admin')->visible(['nickname']);
				$row->visible(['agent']);
				$row->getRelation('agent')->visible(['name']);
            }
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }
    
    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                
//                if(db('hospital')->where(['code'=>$params['code'],'platform_id'=>$params['platform_id']])->find()){
//                    $this->error('医院代码重复');
//                    exit();
//                }
                
                if($params['agent_id']){
                    $agentFcbl = db('agent')->where(['id' => $params['agent_id']])->value('fcbl');
                    if ($agentFcbl < $params['fcbl']) {
                        $this->error('分成比例最大可设置' . $agentFcbl);
                        exit();
                    }
                }else{
                    $platformFcbl = db('platform')->where(['id' => $params['platform_id']])->value('fcbl');
                    if ($platformFcbl < $params['fcbl']) {
                        $this->error('分成比例最大可设置' . $platformFcbl);
                        exit();
                    }
                }
                
                if($params['use_start'] != '' && $params['use_end'] == ''){
                    $this->error('请输入限制结束时间');
                }
                
                if($params['use_end'] != '' && $params['use_start'] == ''){
                    $this->error('请输入限制开始时间');
                }
                
                if($params['charging_rule'] == 2){
                    if($params['contract_price'] == ''){
                        $this->error('请输入包干价');
                    }
                }
                
                $route = $this->getRoute(array(), $params['agent_id']);
                $params['route'] = json_encode($route);
                
                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    
                    $params_admin = $this->request->post("admin/a");
                    if ($params_admin)
                    {
                        if(!db('admin')->where(['username' => $params_admin['username']])->find()){
                            $params_admin['nickname'] = $params['name'];
                            $params_admin['salt'] = Random::alnum();
                            $params_admin['password'] = md5(md5('123456') . $params_admin['salt']);
                            $params_admin['avatar'] = '/assets/img/avatar.png'; //设置新管理员默认头像。
                            $params_admin['status'] = 'normal';
                            $params_admin['createtime'] = time();
                            $params_admin['platform_id'] = $params['platform_id'];
                            $params_admin['types'] = 4;
                            $params_admin['hierarchy'] = 3;
                            $params_admin['details_id'] = $this->model->id;

                            $result = $this->modelAdmin->validate('Admin.add')->save($params_admin);
                            if ($result === false)
                            {
                                db('hospital')->where(['id' => $this->model->id])->delete();
                                $this->error($this->modelAdmin->getError());
                            }

                            $group = array(
                                'uid' => $this->modelAdmin->id,
                                'group_id' => 8,
                            );
                            db('auth_group_access')->insertGetId($group);
                        }else{
                            db('hospital')->where(['id' => $this->model->id])->delete();
                            $this->error('管理员账号不能重复');
                        }
                    }
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }
    
    
    
    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params['agent_id'] != 0) {
                $agentFcbl = db('agent')->where(['id' => $params['agent_id']])->value('fcbl');
                if ($agentFcbl < $params['fcbl']) {
                    $this->error('分成比例最大可设置' . $agentFcbl);
                    exit();
                }
            } else {
                $platformFcbl = db('platform')->where(['id' => $params['platform_id']])->value('fcbl');
                if ($platformFcbl < $params['fcbl']) {
                    $this->error('分成比例最大可设置' . $platformFcbl);
                    exit();
                }
            }
            
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    
                    $params_admin = $this->request->post("admin/a");
                    if ($params_admin)
                    {
                        if(!db('admin')->where(['username' => $params_admin['username'],'details_id'=>['<>',$ids]])->find()){
                            $update = array(
                                'username' => $params_admin['username'],
                                'email' => $params_admin['email'],
                                'updatetime' => time(),
                            );
                            if($params_admin['password'] != ''){
                                $admin = db('admin')->where(['details_id' => $ids])->find();
                                $update['password'] = md5(md5($params_admin['password']) . $admin['salt']);
                            }
                            $result = db('admin')->where(['details_id'=>$ids])->update($update);
                            if (!$result){
                                $this->error('账号更新失败');
                            }
                        }else{
                            $this->error('管理员账号重复');
                        }
                    }
                    
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        $admin = db('admin')->where(['details_id' => $row['id']])->find();
        $this->view->assign("admin", $admin);
        return $this->view->fetch();
    }
    
    /*
     * 生成医院的 route
     * agent_id 上级代理商id
     * route 
     */
    public function getRoute($route,$agent_id){
        $agent = db('agent')->where(['id'=>$agent_id])->find();
        $route[] = array('id'=>$agent['id'],'fcbl'=>$agent['fcbl']);
        if($agent['agent_id'] > 0){
            $route = $this->getRoute($route, $agent['agent_id']);
        }
        return $route;
    }
}
