<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Adposition_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-adposition_id" data-rule="required" data-source="ad/adposition/index" class="form-control selectpage" name="row[adposition_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Info')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-info" class="form-control " rows="5" name="row[info]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ad_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-ad_image" data-rule="required" class="form-control" size="50" name="row[ad_image]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-ad_image" class="btn btn-danger plupload" data-input-id="c-ad_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-ad_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-ad_image" class="btn btn-primary fachoose" data-input-id="c-ad_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-ad_image"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-ad_image"></ul>
        </div>
    </div>
<!--    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-url" class="form-control" name="row[url]" type="text">
        </div>
    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="1"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
