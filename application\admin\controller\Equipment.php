<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\admin\library\Auth;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;
use fast\Random;
/**
 * 主设备管理
 *
 * @icon fa fa-circle-o
 */
class Equipment extends Backend
{
    
    /**
     * Equipment模型对象
     * @var \app\admin\model\Equipment
     */
    protected $model = null;
    protected $hospital_id = 0;
    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Equipment;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("hardwaretypeList", $this->model->getHardwaretypeList());
        $hospital_id = input('hospital_id');
        $this->hospital_id = $hospital_id;
        $this->assign('hospital_id',$hospital_id);
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax())
        {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField'))
            {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                    ->with(['admin','agent','hospital','hospitaldepartments'])
                    ->where($where)
                    ->where(['hospitals_id'=>  $this->hospital_id])
                    ->order($sort, $order)
                    ->count();

            $list = $this->model
                    ->with(['admin','agent','hospital','hospitaldepartments'])
                    ->where($where)
                    ->where(['hospitals_id'=>  $this->hospital_id])
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();

            foreach ($list as $row) {
                $row->visible(['id','platform_id','agent_id','hospitals_id','departments_id','maincode','mainname','notes','status','voltage','voltagetime','createtime','updatetime','hardware_type']);
                $row->visible(['admin']);
				$row->getRelation('admin')->visible(['nickname']);
				$row->visible(['agent']);
				$row->getRelation('agent')->visible(['name']);
				$row->visible(['hospital']);
				$row->getRelation('hospital')->visible(['name']);
				$row->visible(['hospitaldepartments']);
				$row->getRelation('hospitaldepartments')->visible(['deptname']);
            }
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }
    
    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                
//                if(db('equipment')->where(['maincode'=>$params['maincode']])->find()){
//                    $this->error('设备编码重复');
//                    exit();
//                }
                $hospital_departments = db('hospital_departments')->where(['id'=>$params['departments_id']])->find();
                $params['platform_id'] = $hospital_departments['platform_id'];
                $params['agent_id'] = $hospital_departments['agent_id'];
                $params['hospitals_id'] = $hospital_departments['hospital_id'];
                
                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }
}
