{"name": "bootstrap-table", "homepage": "https://github.com/wenzhixin/bootstrap-table", "authors": ["zhixin <<EMAIL>>"], "description": "An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features.", "main": ["src/bootstrap-table.js", "src/bootstrap-table.css"], "keywords": ["bootstrap", "table", "bootstrap table"], "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests", "docs", "assets"], "version": "1.11.1", "_release": "1.11.1", "_resolution": {"type": "version", "tag": "1.11.1", "commit": "d5ccd48950a03a3f7d684358bebf1899bec4d3b0"}, "_source": "https://github.com/wenzhixin/bootstrap-table.git", "_target": "~1.11.0", "_originalSource": "bootstrap-table"}