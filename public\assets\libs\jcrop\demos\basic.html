<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Hello World | Jcrop Demos</title>
    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.10.2/jquery.min.js"></script>
    <script src="../js/Jcrop.js"></script>
    <script type="text/javascript">
      jQuery(function($){
        var d = document, ge = 'getElementById';
        
        $('#interface').on('cropmove cropend',function(e,s,c){
          d[ge]('crop-x').value = c.x;
          d[ge]('crop-y').value = c.y;
          d[ge]('crop-w').value = c.w;
          d[ge]('crop-h').value = c.h;
        });
        
        // Most basic attachment example
        $('#target').Jcrop({
          setSelect: [ 175, 100, 400, 300 ]
        });
        
        $('#text-inputs').on('change','input',function(e){
          $('#target').Jcrop('api').animateTo([
            parseInt(d[ge]('crop-x').value),
            parseInt(d[ge]('crop-y').value),
            parseInt(d[ge]('crop-w').value),
            parseInt(d[ge]('crop-h').value)
          ]);
        });
        
      });
      
    </script>
    <link rel="stylesheet" href="demo_files/main.css">
    <link rel="stylesheet" href="demo_files/demos.css">
    <link rel="stylesheet" href="../css/Jcrop.css">
    <style>
      #text-inputs { margin: 10px 8px 0; }
      .input-group { margin-right: 1.5em; }
      .nav-box { width: 748px; padding: 0 !important; margin: 4px 0; background-color: #f8f8f7; }
      
    </style>
  </head>
  <body>
    <div class="navbar navbar-fixed-top">
      <div class="navbar-inner">
        <div class="container">
          <button type="button" data-toggle="collapse" data-target="nav-collapse" class="btn btn-navbar"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></button><a href="../" class="brand">Jcrop</a>
          <div class="nav-collapse collapse">
            <ul class="nav">
              <li class="active"><a href="./basic.html">Demos</a>
              </li>
              <li><a href="http://beta.jcrop.org/doc/">Docs</a>
              </li>
              <li><a href="http://beta.jcrop.org/contact/">Contact</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="container">
      <div class="row">
        <div class="span12">
          <div class="jc-demo-box">
            <div class="page-header">
              <h1>Hello World</h1>
            </div>
            <div class="demo-nav menu-box">
              <h3>Jcrop Demos</h3>
              <ul class="links">
                <li><b>Hello World</b></li>
                <li><a href="thumbnail.html">Thumbnail Preview</a></li>
                <li><a href="panel.html">Feature Panel</a></li>
                <li><a href="coords.html">Dimensions</a></li>
                <li><a href="circle.html">Circles</a></li>
              </ul>
            </div>
            <div id="interface" class="page-interface"><img src="http://jcrop-cdn.tapmodo.com/assets/images/sierra2-750.jpg" id="target"></div>
            <div class="nav-box">
              <form onsubmit="return false;" id="text-inputs"><span class="input-group"><b>X</b>
                  <input type="text" name="cx" id="crop-x" class="span1"></span><span class="input-group"><b>Y</b>
                  <input type="text" name="cy" id="crop-y" class="span1"></span><span class="input-group"><b>W</b>
                  <input type="text" name="cw" id="crop-w" class="span1"></span><span class="input-group"><b>H</b>
                  <input type="text" name="ch" id="crop-h" class="span1"></span>
              </form>
            </div>
            <div class="tapmodo-footer"><a href="http://tapmodo.com" class="tapmodo-logo segment">tapmodo.com</a>
              <div class="segment"><b>&copy; 2008-2013 Tapmodo Interactive LLC</b>
                <div>Jcrop is free software released under <a href="../MIT-LICENSE.txt">MIT License</a></div>
              </div>
            </div>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>