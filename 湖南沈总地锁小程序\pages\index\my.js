// pages/index/my.js
const app = getApp()
Page({

	/**
	 * 页面的初始数据
	 */
	data: {
		customBar: app.globalData.customBar,

		islogin: true,

		nickname: '',
		avatar: '',
		mobile:'',

		order: [
			{
				type: '',
				pic: '/image/icon_order1.png',
				title: '全部订单',
			},
			{
				type: '1',
				pic: '/image/icon_order2.png',
				title: '使用中',
			},
			{
				type: '2',
				pic: '/image/icon_order3.png',
				title: '待支付',
			},
			{
				type: '3',
				pic: '/image/icon_order4.png',
				title: '已完成',
			},
		],

		nav: [
			{
				path: '/packageB/instruction/index',
				pic: '/image/icon_nav1.png',
				title: '使用说明',
			},
			{
				path: '/packageB/setrecords/index',
				pic: '/image/icon_nav2.png',
				title: '购买套餐记录',
			},
			{
				path: '/packageB/charge/index',
				pic: '/image/icon_nav3.png',
				title: '收费方式',
			},
		],

		kftel: '400-8866-537',// 客服电话
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad(options) {
		this.getConfig()
	},


	// 系统配置
	getConfig(){
		let that = this;
		let params = {}
		app.post('Ajax/getConfig', params).then(res => {
			const {
				code,
				data,
				msg
			} = res //接口数据
			if (code == 1) {
				that.setData({
					kftel: data.telephone
				});
			} else {
				wx.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				})
			}
		}).catch((err) => {

		})
	},

	//授权登录
	getLogin() {
		app.toLogin().then(res => {
			if(wx.getStorageSync("SDKVersion") >= '2.21.2'){
				wx.redirectTo({
					url: 'login?type=2'
				})
			}else{
				this.setData({
					islogin: false,
					mobile: wx.getStorageSync("mobile"),
					nickname: wx.getStorageSync("nickname"),
					avatar: wx.getStorageSync("avatar"),
				});
			}
		}).catch(() => {

		});
	},


	// 手机号绑定（使用公共方法）
	getPhoneNumber(e) {
		let that = this;

		// 使用app.js中的公共方法获取手机号
		app.getWechatPhoneNumber(e,
			// 成功回调
			function(mobile) {
				// 更新页面显示的手机号
				that.setData({
					mobile: mobile,
				});
			},
			// 失败回调
			function(errorMsg) {
				console.log('获取手机号失败：', errorMsg);
				// 错误提示已在公共方法中处理，这里可以添加额外的业务逻辑
			}
		);
	},


	// 购买套餐
	goPackage() {
		if (!wx.getStorageSync("token")) {
			wx.showToast({
				title: '请先授权登录',
				icon: 'none',
				duration: 2000
			})
		} else {
			wx.navigateTo({
				url: '/packageA/package/index',
			})
		}
	},

	// 订单
	goOrder(e){
		if (!wx.getStorageSync("token")) {
			wx.showToast({
				title: '请先授权登录',
				icon: 'none',
				duration: 2000
			})
		} else {
			wx.navigateTo({
				url: '/packageB/order/index?type=' + e.currentTarget.dataset.type,
			})
		}
	},

	// 跳转
	goUrl(e){
		if (!wx.getStorageSync("token")) {
			wx.showToast({
				title: '请先授权登录',
				icon: 'none',
				duration: 2000
			})
		} else {
			wx.navigateTo({
				url: e.currentTarget.dataset.path,
			})
		}
	},

	//拨打电话事件
	call(e) {
		var tel = e.currentTarget.dataset.tel
		wx.makePhoneCall({
			phoneNumber: tel,
		})
	},

	/**
	 * 生命周期函数--监听页面初次渲染完成
	 */
	onReady() {

	},

	/**
	 * 生命周期函数--监听页面显示
	 */
	onShow() {
		let that = this;
		if (wx.getStorageSync("token")) {
			that.setData({
				islogin: false,
				mobile: wx.getStorageSync("mobile"),
				nickname: wx.getStorageSync("nickname"),
				avatar: wx.getStorageSync("avatar"),
			});
		};
	},

	/**
	 * 生命周期函数--监听页面隐藏
	 */
	onHide() {

	},

	/**
	 * 生命周期函数--监听页面卸载
	 */
	onUnload() {

	},

	/**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
	onPullDownRefresh() {

	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {

	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {

	}
})