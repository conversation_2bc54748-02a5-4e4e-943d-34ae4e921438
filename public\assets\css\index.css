html,
body {
    height: 100%;
    width: 100%;
}
body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-family: "Roboto", "SF Pro SC", "SF Pro Display", "SF Pro Icons", "PingFang SC", BlinkMacSystemFont, -apple-system, "Segoe UI", "Microsoft Yahei", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
    font-weight: 400;
}
a {
    -webkit-transition: all 0.35s;
    -moz-transition: all 0.35s;
    transition: all 0.35s;
    color: #fdcc52;
}
a:hover,
a:focus {
    color: #fcbd20;
}
hr {
    max-width: 100px;
    margin: 25px auto 0;
    border-width: 1px;
    border-color: rgba(34, 34, 34, 0.1);
}
hr.light {
    border-color: white;
}
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 200;
    letter-spacing: 1px;
}
p {
    font-size: 16px;
    line-height: 1.5;
    margin-bottom: 20px;
}
.navbar-default {
    background-color: white;
    border-color: rgba(34, 34, 34, 0.05);
    -webkit-transition: all 0.35s;
    -moz-transition: all 0.35s;
    transition: all 0.35s;
    font-family: 'Catamaran', 'Helvetica', 'Arial', 'sans-serif';
    font-weight: 200;
    letter-spacing: 1px;
}
.navbar-default .navbar-header .navbar-brand {
    font-family: 'Catamaran', 'Helvetica', 'Arial', 'sans-serif';
    font-weight: 200;
    letter-spacing: 1px;
    color: #fdcc52;
}
.navbar-default .navbar-header .navbar-brand:hover,
.navbar-default .navbar-header .navbar-brand:focus {
    color: #fcbd20;
}
.navbar-default .navbar-header .navbar-toggle {
    font-size: 12px;
    color: #222222;
    padding: 8px 10px;
}
.navbar-default .nav > li > a {
    text-transform: uppercase;
    letter-spacing: 2px;
    font-size: 14px;
}
.navbar-default .nav > li > a,
.navbar-default .nav > li > a:focus {
    color: #222222;
}
.navbar-default .nav > li > a:hover,
.navbar-default .nav > li > a:focus:hover {
    color: #fdcc52;
}
.navbar-default .nav > li.active > a,
.navbar-default .nav > li.active > a:focus {
    color: #fdcc52 !important;
    background-color: transparent;
}
.navbar-default .nav > li.active > a:hover,
.navbar-default .nav > li.active > a:focus:hover {
    background-color: transparent;
}
.navbar-default .navbar-header .navbar-brand {
    padding-top:3px;
}
@media (min-width: 768px) {
    .navbar-default {
        background-color: transparent;
        border-color: transparent;
    }
    .navbar-default .navbar-header .navbar-brand {
        color: rgba(255, 255, 255, 0.7);
    }
    .navbar-default .navbar-header .navbar-brand:hover,
    .navbar-default .navbar-header .navbar-brand:focus {
        color: white;
    }
    .navbar-default .nav > li > a,
    .navbar-default .nav > li > a:focus {
        color: rgba(255, 255, 255, 0.7);
    }
    .navbar-default .nav > li > a:hover,
    .navbar-default .nav > li > a:focus:hover {
        color: white;
    }
    .navbar-default.affix {
        background-color: white;
        border-color: rgba(34, 34, 34, 0.1);
    }
    .navbar-default.affix .navbar-header .navbar-brand {
        color: #222222;
    }
    .navbar-default.affix .navbar-header .navbar-brand:hover,
    .navbar-default.affix .navbar-header .navbar-brand:focus {
        color: #fdcc52;
    }
    .navbar-default.affix .nav > li > a,
    .navbar-default.affix .nav > li > a:focus {
        color: #222222;
    }
    .navbar-default.affix .nav > li > a:hover,
    .navbar-default.affix .nav > li > a:focus:hover {
        color: #fdcc52;
    }
}
header {
    position: relative;
    width: 100%;
    min-height: auto;
    overflow-y: hidden;
    background: url("../img/bg-pattern.png"), #7b4397;
    /* fallback for old browsers */
    background: url("../img/bg-pattern.png"), -webkit-linear-gradient(to left, #328944, #247cdc);
    /* Chrome 10-25, Safari 5.1-6 */
    background: url(../img/bg-pattern.png), linear-gradient(to left, #328944, #247cdc);
    /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
    color: white;
}
header .header-content {
    text-align: center;
    padding: 150px 0 50px;
    position: relative;
}
header .header-content .header-content-inner {
    position: relative;
    margin: 0 auto;
}
header .header-content .header-content-inner h1 {
    margin-top: 0;
    margin-bottom: 30px;
    font-size: 80px;
}
header .header-content .header-content-inner .list-badges {
    margin-bottom: 25px;
}
header .header-content .header-content-inner .list-badges img {
    height: 50px;
    margin-bottom: 25px;
}
header .device-container {
    max-width: 300px;
    margin: 0 auto 100px;
}
header .device-container .screen img {
    border-radius: 3px;
}
@media (min-width: 768px) {
    header {
        min-height: 100%;
    }
    header .header-content {
        text-align: center;
        padding: 0;
        height: 100vh;
    }
    header .header-content .header-content-inner {
        width:100%;
        margin: 0;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
    }
    header .header-content .header-content-inner h1 {
        font-size: 35px;
    }
    header .device-container {
        max-width: none;
        max-height: calc(0vh);
        margin: 100px auto 0;
    }
}
@media (min-width: 992px) {
    header .header-content .header-content-inner h1 {
        font-size: 80px;
    }
}

@media (max-width: 767px) {
    header .header-content .header-content-inner h1 {
        font-size: 50px;
    }
}
section {
    padding: 100px 0;
}
section h2 {
    font-size: 50px;
}
section.download {
    padding: 150px 0;
    position: relative;
}
section.download h2 {
    margin-top: 0;
    font-size: 50px;
}
section.download .badges .badge-link {
    display: block;
    margin-bottom: 25px;
}
section.download .badges .badge-link:last-child {
    margin-bottom: 0;
}
section.download .badges .badge-link img {
    height: 60px;
}
@media (min-width: 768px) {
    section.download .badges .badge-link {
        display: inline-block;
        margin-bottom: 0;
    }
}
@media (min-width: 768px) {
    section.download h2 {
        font-size: 70px;
    }
}
section.features .section-heading {
    margin-bottom: 100px;
}
section.features .section-heading h2 {
    margin-top: 0;
}
section.features .section-heading p {
    margin-bottom: 0;
}
section.features .device-container,
section.features .feature-item {
    max-width: 300px;
    margin: 0 auto;
    height:215px;
}
section.features .device-container {
    margin-bottom: 100px;
}
@media (min-width: 992px) {
    section.features .device-container {
        margin-bottom: 0;
    }
}
section.features .feature-item {
    text-align: center;
    margin-bottom: 80px;
}
section.features .feature-item h3 {
    font-size: 24px;
}
section.features .feature-item i {
    font-size: 60px;
    
    background: -webkit-linear-gradient(to left,#328944, #247cdc);
    background: linear-gradient(to left, #328944, #247cdc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
@media (min-width: 992px) {
    section.features .device-container,
    section.features .feature-item {
        max-width: none;
    }
}
section.cta {
    position: relative;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    background-size: cover;
    -o-background-size: cover;
    background-position: center;
    background-image: url('../img/bg-middle.jpg');
    padding: 250px 0;
}
section.cta .cta-content {
    position: relative;
    z-index: 1;
}
section.cta .cta-content h2 {
    margin-top: 0;
    margin-bottom: 25px;
    color: white;
    max-width: 450px;
    font-size: 50px;
}
@media (min-width: 768px) {
    section.cta .cta-content h2 {
        font-size: 70px;
    }
}
section.cta .overlay {
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    position: absolute;
    top: 0;
    left: 0;
}
section.contact {
    text-align: center;
}
section.contact h2 {
    margin-top: 0;
    margin-bottom: 25px;
}
section.contact h2 i {
    color: #dd4b39;
}
section.contact ul.list-social {
    margin-bottom: 0;
}
section.contact ul.list-social li a {
    display: block;
    height: 80px;
    width: 80px;
    line-height: 80px;
    font-size: 40px;
    border-radius: 100%;
    color: white;
}
section.contact ul.list-social li.social-github a {
    background-color: #444;
}
section.contact ul.list-social li.social-github a:hover {
    background-color: #111;
}
section.contact ul.list-social li.social-qq a {
    background-color: #1da1f2;
}
section.contact ul.list-social li.social-qq a:hover {
    background-color: #0d95e8;
}
section.contact ul.list-social li.social-weibo a {
    background-color: #dd4b39;
}
section.contact ul.list-social li.social-weibo a:hover {
    background-color: #d73925;
}
footer {
    background-color: #222222;
    padding: 25px 0;
    color: rgba(255, 255, 255, 0.3);
    text-align: center;
}
footer p {
    font-size: 12px;
    margin: 0;
}
footer ul {
    margin-bottom: 0;
}
footer ul li a {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.3);
}
footer ul li a:hover,
footer ul li a:focus,
footer ul li a:active,
footer ul li a.active {
    text-decoration: none;
}
.bg-primary {
    background: #fdcc52;
    background: -webkit-linear-gradient(#fdcc52, #fdc539);
    background: linear-gradient(#fdcc52, #fdc539);
}
.text-primary {
    color: #fdcc52;
}
.no-gutter > [class*='col-'] {
    padding-right: 0;
    padding-left: 0;
}
.btn-outline {
    color: white;
    border-color: white;
    border: 1px solid;
}
.btn-outline:hover,
.btn-outline:focus,
.btn-outline:active,
.btn-outline.active {
    color: white;
    background-color: #fdcc52;
    border-color: #fdcc52;
}
.btn {
    text-transform: uppercase;
    letter-spacing: 2px;
    border-radius: 300px;
}
.btn-xl {
    margin-top:20px;
    padding: 10px 45px;
    font-size: 14px;
}
body {
    webkit-tap-highlight-color: #222222;
}
