<?php
namespace Smsplug;

use think\Db;
use think\Config;
use think\Session;
use think\Request;
class Smsplug
{
    private $username;
    private $password;
    private $comid;
    private $company_name;
    private $errNo = array(
        "1" => "短信发送成功",
        "-2" => "除时间外，所有参数不能为空",
        "-3" => "用户名密码不正确",
        "-4" => "平台不存在",
        "-5" => "客户短信数量为0",
        "-6" => "客户账户余额小于要发送的条数",
        "-7" => "不能超过70个字",
        "-8" => "非法短信内容",
        "-9" => "未知系统故障",
        "-10" => "网络性错误",
        "-21" => "代表要加签名"
    );

    public function __construct()
    {
        $this->username = 'gongxiangchuang';
        $this->password = 'gongxiangchuang2019';
        $this->comid = '4047';
        $this->company_name = '【康护云】';
    }

    public function getError($no)
    {
        return $this->errNo[$no];
    }

    public function sendSms($mobile, $sms_content)
    {
        if (empty($mobile) || empty($sms_content)) {
            return false;
        }
        $mobile = urlencode(iconv("UTF-8","gbk",$mobile));
        $sms_content = urlencode(iconv("UTF-8","gbk",$sms_content . $this->company_name));
        $time = time();
        $sendUrl = 'http://jiekou.56dxw.com/sms/HttpInterface.aspx?comid=' . $this->comid . '&username=' . $this->username . '&userpwd=' . $this->password . '&handtel=' .$mobile . '&sendcontent=' .$sms_content . '&sendtime='. $time .'&smsnumber=10690';
//        die($sendUrl);
        $sendNo = file_get_contents($sendUrl);

        if($sendNo == 1){
            return [
                'status' => 1,
                'msg' => '发送短信成功'
            ];
        }else{
            return [
                'status' => 0,
                'msg' => $this->getError($sendNo)
            ];
        }

    }
}