<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-field="nickname" data-source="user/user" class="form-control selectpage" name="row[user_id]" type="text" value="">
        </div>
    </div>
    <!--判断管理员等级  -->
    {if condition="($admin.hierarchy <= 1)"}
    <!--    <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Platform_id')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-platform_id" data-rule="required" data-field="name" data-source="platform/index" class="form-control selectpage" name="row[platform_id]" type="text" value="">
            </div>
        </div>-->
    {else /}
    <!--<input id="c-platform_id" name="row[platform_id]" type="hidden" value="{$admin.platform_id}" />-->
    {/if}
    <input id="c-platform_id" name="row[platform_id]" type="hidden" value="{$platform_id}" />

    {if condition="($admin.hierarchy < 3)"}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_id"  data-rule="required" data-source="agent/index" class="form-control selectpage" name="row[agent_id]" type="text" value="">
        </div>
    </div>
    {else /}
    <input id="c-agent_id" name="row[agent_id]" type="hidden" value="{$admin.details_id}" />
    {/if}


    <!--    <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">{:__('Code')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-code" data-rule="required" class="form-control" name="row[code]" type="text">
            </div>
        </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Addr')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-addr" data-rule="required" class="form-control" name="row[addr]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Latitude_longitude')}:</label>
        <div class="col-xs-12 col-sm-3">
            <input id="c-longitude" data-rule="required" class="form-control" name="row[longitude]" type="text"
                   placeholder="{:__('Longitude')}">
        </div>
        <div class="col-xs-12 col-sm-3">
            <input id="c-latitude" data-rule="required" class="form-control" name="row[latitude]" type="text"
                   placeholder="{:__('Latitude')}">
        </div>
        <div class="col-xs-12 col-sm-2">
            <button type="button" class="btn btn-primary" data-toggle="addresspicker" data-input-id="c-address"
                    data-lng-id="c-longitude" data-lat-id="c-latitude">{:__('Obtain')}
            </button>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Join_type')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-join_type" data-rule="required" class="form-control selectpicker" name="row[join_type]">
                {foreach name="joinTypeList" item="vo"}
                <option value="{$key}" {in name="key" value="1"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Fcbl')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-fcbl" data-rule="required" class="form-control" step="0.01" name="row[fcbl]" type="number">
        </div>
    </div>





    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Use_start')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-use_start" class="form-control datetimepicker" data-date-format="HH" data-use-current="true" name="row[use_start]" type="text">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Use_end')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-use_end" class="form-control datetimepicker" data-date-format="HH" data-use-current="true" name="row[use_end]" type="text">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Overtime_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-overtime_price" data-rule="required" class="form-control" step="0.01" name="row[overtime_price]" type="number" value="0.00">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Charging_rule')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select  id="c-join_type" data-rule="required" class="form-control selectpicker" name="row[charging_rule]">
                {foreach name="chargingruleList" item="vo"}
                <option value="{$key}" {in name="key" value="1"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Contract_price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-contract_price" data-rule="required" class="form-control" step="0.01" name="row[contract_price]" type="number" value="0.00">
        </div>
    </div>





    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-price" data-rule="required" class="form-control" step="0.01" name="row[price]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hourlong')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hourlong" data-rule="required" class="form-control" name="row[hourlong]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Freedt')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-freedt" data-rule="required" class="form-control" name="row[freedt]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Logo_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-logo_image" data-rule="required" class="form-control" size="50" name="row[logo_image]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-logo_image" class="btn btn-danger plupload" data-input-id="c-logo_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-logo_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-logo_image" class="btn btn-primary fachoose" data-input-id="c-logo_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-logo_image"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-logo_image"></ul>
        </div>
    </div>
<!--    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Notes')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-notes" class="form-control " rows="5" name="row[notes]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Corpname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-corpname" class="form-control" name="row[corpname]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Kefu')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-kefu" class="form-control" name="row[kefu]" type="text">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Introduce_content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-introduce_content" class="form-control editor" rows="5" name="row[introduce_content]" cols="50"></textarea>
        </div>
    </div>-->

    <div class="form-group">
        <label for="username" class="control-label col-xs-12 col-sm-2">管理员账号:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="username" name="admin[username]" value="" data-rule="required;username" />
        </div>
    </div>
    <div class="form-group">
        <label for="email" class="control-label col-xs-12 col-sm-2">Email:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="email" class="form-control" id="email" name="admin[email]" value="" data-rule="required;email" />
        </div>
    </div>
    <!--    <div class="form-group">
            <label for="nickname" class="control-label col-xs-12 col-sm-2">昵称:</label>
            <div class="col-xs-12 col-sm-8">
                <input type="text" class="form-control" id="nickname" name="admin[nickname]" autocomplete="off" value="" data-rule="required" />
            </div>
        </div>-->

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">

            <div class="radio">
                {foreach name="statusList" item="vo"}
                <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="1"}checked{/in} /> {$vo}</label> 
                {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
