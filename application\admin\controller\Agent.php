<?php

namespace app\admin\controller;

use app\common\controller\Backend;

use app\admin\library\Auth;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;
use fast\Random;

/**
 * 代理商
 *
 * @icon fa fa-circle-o
 */
class Agent extends Backend
{
    
    /**
     * Agent模型对象
     * @var \app\admin\model\Agent
     */
    protected $model = null;
    protected $modelAdmin = null;
    protected $noNeedRight = ['getAgentLilst'];
    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Agent;
        $this->modelAdmin = model('Admin');
        $this->view->assign("statusList", $this->model->getStatusList());
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax())
        {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField'))
            {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            list($mywhere) = $this->mybuildparams();
            $total = $this->model
                    ->with(['admin'])
                    ->where($where)
                    ->where($mywhere)
                    ->order($sort, $order)
                    ->count();

            $list = $this->model
                    ->with(['admin'])
                    ->where($where)
                     ->where($mywhere)
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();

            foreach ($list as $row) {
                $row['agent_id_name'] = db('agent')->where(['id'=>$row['agent_id']])->value('name');
                if(!$row['agent_id_name']){
                    $row['agent_id_name'] = '无';
                }
                $row['platform_id_name'] = db('platform')->where(['id'=>$row['platform_id']])->value('name');
                $row->visible(['id','platform_id','platform_id_name','agent_id','agent_id_name','code','name','notes','join_time','fcbl','province','city','area','status','createtime','updatetime','admin_id','balance']);
                $row->visible(['admin']);
                $row->getRelation('admin')->visible(['nickname']);
            }
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }
    
    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $params['platform_id'] = $this->platform_id;
                $params['agent_id'] = 0;
//                if(db('agent')->where(['code'=>$params['code'],'platform_id'=>$params['platform_id']])->find()){
//                    $this->error('代理商代码重复');
//                    exit();
//                }
                
                $fcblReturn = $this->fcblMaximum($params);
                if(!$fcblReturn['success']){
                    $this->error($fcblReturn['msg']);
                    exit();
                }
                

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    
                    $params_admin = $this->request->post("admin/a");
                    if ($params_admin)
                    {
                        if(!db('admin')->where(['username' => $params_admin['username']])->find()){
                            $params_admin['nickname'] = $params['name'];
                            $params_admin['salt'] = Random::alnum();
                            $params_admin['password'] = md5(md5('123456') . $params_admin['salt']);
                            $params_admin['avatar'] = '/assets/img/avatar.png'; //设置新管理员默认头像。
                            $params_admin['status'] = 'normal';
                            $params_admin['createtime'] = time();
                            $params_admin['platform_id'] = $params['platform_id'];
                            $params_admin['types'] = 3;
                            $params_admin['hierarchy'] = 3;
                            $params_admin['details_id'] = $this->model->id;

                            $result = $this->modelAdmin->validate('Admin.add')->save($params_admin);
                            if ($result === false)
                            {
                                db('agent')->where(['id' => $this->model->id])->delete();
                                $this->error($this->modelAdmin->getError());
                            }

                            $group = array(
                                'uid' => $this->modelAdmin->id,
                                'group_id' => 6,
                            );
                            db('auth_group_access')->insertGetId($group);
                        }else{
                            db('agent')->where(['id' => $this->model->id])->delete();
                            $this->error('管理员账号不能重复');
                        }
                        
                    }
                    
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }
    
    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            
            
            $fcblReturn = $this->fcblMaximum($params);
            if(!$fcblReturn['success']){
                $this->error($fcblReturn['msg']);
                exit();
            }
            
            $res = db('agent')->where(['agent_id' => $ids, 'fcbl' => ['>', $params['fcbl']]])->find();
            if ($res) {
                $this->error('下级代理商分成比例有大于该数值的，请先联系下级进行修改', '', '', 3000);
                exit;
            }
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->hospitalRouteUpdate($ids,$params['fcbl'],$ids);
                    
                    $params_admin = $this->request->post("admin/a");
                    if ($params_admin)
                    {
                        if(!db('admin')->where(['username' => $params_admin['username'],'details_id'=>['<>',$ids]])->find()){
                            $update = array(
                                'username' => $params_admin['username'],
                                'email' => $params_admin['email'],
                                'updatetime' => time(),
                            );
                            if($params_admin['password'] != ''){
                                $admin = db('admin')->where(['details_id' => $ids])->find();
                                $update['password'] = md5(md5($params_admin['password']) . $admin['salt']);
                            }
                            $result = db('admin')->where(['details_id'=>$ids])->update($update);
                            if (!$result){
                                $this->error('账号更新失败');
                            }
                        }else{
                            $this->error('管理员账号重复');
                        }
                    }
                    
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        
        $admin = db('admin')->where(['details_id' => $row['id']])->find();
        $this->view->assign("admin", $admin);
        return $this->view->fetch();
    }
    
    public function fcblMaximum($params) {
        $return = array(
            'success' => false,
        );
        if ($params['agent_id'] != '' && $params['agent_id'] != 0) {
            $agentFcbl = db('agent')->where(['id' => $params['agent_id']])->value('fcbl');
            if ($agentFcbl < $params['fcbl']) {
                $return['msg'] = '分成比例最大可设置' . $agentFcbl;
            }else{
                $return['success'] = true;
            }
        } else {
            $platformFcbl = db('platform')->where(['id' => $params['platform_id']])->value('fcbl');
            if ($platformFcbl < $params['fcbl']) {
                $return['msg'] = '分成比例最大可设置' . $platformFcbl;
            }else{
                $return['success'] = true;
            }
        }
        return $return;
    }
    
    /*
     * 修改医院的route
     * uid 修改分成比例的代理商id
     * fcbl 修改后的分成比例
     * pid 上级代理商id
     */
    public function hospitalRouteUpdate($uid,$fcbl,$pid){
        //查询代理商下的所有医院
        $hospital = db('hospital')->where(['agent_id'=>$pid])->select();
        if($hospital){
            //循环代理商下的医院
            foreach($hospital as $k => $v){
                $route = json_decode($hospital[$k]['route'],true);
                foreach($route as $k1 => $v1){
                    if($route[$k1]['id'] == $uid){
                        $route[$k1]['fcbl'] = $fcbl;
                    }
                }
                $hospital_update = array(
                    'route' => json_encode($route)
                );
                db('hospital')->where(['id'=>$hospital[$k]['id']])->update($hospital_update);
            }
        }
        
        //查询代理商的下级代理商
        $agent = db('agent')->where(['agent_id'=>$pid])->select();
        foreach($agent as $k => $v){
            $this->hospitalRouteUpdate($uid, $fcbl, $agent[$k]['id']);
        }
    }
    /*
     * 获得所有的代理商
     */
    public function getAgentLilst(){
        $input = input();
        $platform_id = $input['custom']['platform_id'];
        $list = $this->model->where(['platform_id'=>$platform_id])->field('id,name')->select();
        $return = array(
            'list' => $list,
            'total' => count($list),
        );
        return json($return);
    }
}
